import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */

  // Enhanced configuration for Solana wallet adapters and HMR stability
  experimental: {
    // Optimize for better HMR stability with dynamic imports
    optimizePackageImports: [
      '@solana/wallet-adapter-phantom',
      '@solana/wallet-adapter-solflare',
      '@solana/wallet-adapter-backpack',
      '@solana/wallet-adapter-glow',
      '@solana/wallet-adapter-base',
      '@solana/wallet-adapter-react',
    ],
  },

  // Webpack configuration for better module handling
  webpack: (config, { dev, isServer }) => {
    // Enhanced configuration for wallet adapters in development
    if (dev && !isServer) {
      // Improve HMR stability for wallet adapter modules
      config.optimization = {
        ...config.optimization,
        moduleIds: 'named', // Use named module IDs for better HMR stability
      };

      // Add specific handling for wallet adapter modules
      config.module.rules.push({
        test: /node_modules\/@solana\/wallet-adapter-/,
        sideEffects: false, // Mark wallet adapters as side-effect free for better tree shaking
      });
    }

    return config;
  },
};

export default nextConfig;
