'use client';

import { MainLayout } from '@/components/layout/MainLayout';

import { WalletButton } from '@/components/wallet/WalletButton';
import { TokenCard, TokenCardSkeleton } from '@/components/trading/TokenCard';
import { LiveIndicator, PulseDot, BreathingCard, ShimmerText } from '@/components/ui/LiveIndicator';
import { useWallet } from '@/hooks/useWallet';
import { APP_CONFIG } from '@/lib/constants';
import { formatSOL, formatCurrency, formatPercentage } from '@/lib/utils';
import {
  getDegenTerm,
  formatDegenAmount,
  formatDegenPercentage,
  getDegenGreeting,
  DEGEN_EMOJIS
} from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function Home() {
  const { connected, balance, publicKey } = useWallet();
  const router = useRouter();

  // Mock data for demonstration
  const mockTokens = [
    {
      tokenAddress: 'So11111111111111111111111111111111111111112',
      tokenSymbol: 'SOL',
      tokenName: 'Solana',
      price: 98.45,
      priceChange24h: 5.2,
      marketCap: 45000000000,
      volume24h: 2500000000,
      multiplierFromSignal: 1.2,
      signalDate: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    },
    {
      tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      tokenSymbol: 'USDC',
      tokenName: 'USD Coin',
      price: 1.0,
      priceChange24h: 0.1,
      marketCap: 25000000000,
      volume24h: 5000000000,
    },
    {
      tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      tokenSymbol: 'BONK',
      tokenName: 'Bonk',
      price: 0.000025,
      priceChange24h: 15.8,
      marketCap: 1500000000,
      volume24h: 150000000,
      multiplierFromSignal: 3.4,
      signalDate: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    },
  ];

  return (
    <MainLayout>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="text-center slide-up-enter">
          <ShimmerText speed="slow">
            <h1 className="text-4xl font-bold text-primary sm:text-5xl">
              Command Center
            </h1>
          </ShimmerText>
          <p className="mt-3 text-xl text-secondary font-medium fade-in-enter">
            Your Solana Alpha Dashboard - Ready to Trade
          </p>
          <div className="mt-6 h-1 w-32 mx-auto bg-neutral-600 rounded-full"></div>
        </div>

        {!connected ? (
          /* Wallet Connection Prompt */
          <div className="max-w-md mx-auto bg-gray-900 border-2 border-green-500/60 rounded-2xl p-4 hover:border-green-400 hover:shadow-lg hover:shadow-green-500/20 transition-all duration-300 relative overflow-hidden">
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-6 border border-gray-700/30 relative z-10 space-y-6">
              <h2 className="text-center text-2xl font-bold text-white">
                Connect Wallet
              </h2>
              <p className="text-gray-300 text-center text-lg font-medium">
                Link your wallet to start aping into alpha signals
              </p>
              <div className="text-center text-sm text-gray-400 bg-gray-700/50 p-3 rounded-lg border border-gray-600/50">
                Only diamond hands allowed
              </div>
              <WalletButton size="lg" className="w-full bg-green-500 hover:bg-green-600 text-white font-bold transition-all duration-200 hover:shadow-lg hover:shadow-green-500/30" />
            </div>
          </div>
        ) : (
          /* Dashboard Content */
          <div className="space-y-8">
            {/* Bag Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-900 border-2 border-green-500/60 rounded-2xl p-4 hover:border-green-400 hover:shadow-lg hover:shadow-green-500/20 transition-all duration-300 relative overflow-hidden">
                <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 relative z-10">
                  <div className="text-sm font-medium text-green-400 uppercase tracking-wide mb-3">
                    Total Bag Value
                  </div>
                  <LiveIndicator
                    value={balance ? (balance.totalValue || balance.sol * 98.45) : 0}
                    type="price"
                    className="text-3xl font-bold text-white"
                  >
                    {balance ? formatCurrency(balance.totalValue || balance.sol * 98.45) : 'Loading...'}
                  </LiveIndicator>
                  <p className="text-sm text-gray-400 mt-2">
                    +2.5% Today
                  </p>
                </div>
              </div>

              <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-4 hover:border-gray-600 hover:shadow-lg hover:shadow-gray-500/10 transition-all duration-300 relative overflow-hidden">
                <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 relative z-10">
                  <div className="text-sm font-medium text-gray-400 uppercase tracking-wide mb-3">
                    SOL Balance
                  </div>
                  <LiveIndicator
                    value={balance ? balance.sol : 0}
                    type="volume"
                    className="text-3xl font-bold text-white"
                  >
                    {balance ? formatSOL(balance.sol) : 'Loading...'}
                  </LiveIndicator>
                  <p className="text-sm text-gray-400 mt-2">
                    Ready to Trade
                  </p>
                </div>
              </div>

              <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-4 hover:border-gray-600 hover:shadow-lg hover:shadow-gray-500/10 transition-all duration-300 relative overflow-hidden">
                <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 relative z-10">
                  <div className="text-sm font-medium text-gray-400 uppercase tracking-wide mb-3">
                    Active Signals
                  </div>
                  <LiveIndicator
                    value={3}
                    type="default"
                    className="text-3xl font-bold text-white"
                    showPulse={true}
                  >
                    3
                  </LiveIndicator>
                  <p className="text-sm text-gray-400 mt-2">
                    2 Fresh Today
                  </p>
                </div>
              </div>
            </div>

            {/* Recent Alpha Signals */}
            <div className="fade-in-enter">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">
                  Fresh Alpha Signals
                </h2>
                <div className="bg-green-500/20 text-green-400 border border-green-500/60 px-3 py-1 rounded-full text-sm font-medium">
                  LIVE
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {mockTokens.map((token) => (
                  <TokenCard
                    key={token.tokenAddress}
                    {...token}
                    onTrade={(action) => {
                      console.log(`${action} ${token.tokenSymbol}`);
                      // Navigate to trading page for manual execution
                      router.push(`/trading?token=${token.tokenAddress}&action=${action}`);
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-4 hover:border-gray-600 hover:shadow-lg hover:shadow-gray-500/10 transition-all duration-300 relative overflow-hidden">
              <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-4 border border-gray-700/30 relative z-10">
                <h3 className="text-xl font-bold text-white mb-4">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Link href="/signals" className="text-center p-6 bg-gray-700/50 border border-gray-600/50 rounded-lg cursor-pointer hover:border-green-500/60 hover:bg-gray-600/50 transition-all duration-200">
                    <div className="w-12 h-12 mx-auto mb-3 bg-gray-600 rounded-full flex items-center justify-center">
                      <Icon name="signals" size="lg" color="white" />
                    </div>
                    <h4 className="font-semibold text-white">View Alpha</h4>
                  </Link>
                  <Link href="/trading" className="text-center p-6 bg-gray-700/50 border border-gray-600/50 rounded-lg cursor-pointer hover:border-green-500/60 hover:bg-gray-600/50 transition-all duration-200">
                    <div className="w-12 h-12 mx-auto mb-3 bg-gray-600 rounded-full flex items-center justify-center">
                      <Icon name="trading" size="lg" color="white" />
                    </div>
                    <h4 className="font-semibold text-white">Trading Mode</h4>
                  </Link>
                  <Link href="/portfolio" className="text-center p-6 bg-gray-700/50 border border-gray-600/50 rounded-lg cursor-pointer hover:border-green-500/60 hover:bg-gray-600/50 transition-all duration-200">
                    <div className="w-12 h-12 mx-auto mb-3 bg-gray-600 rounded-full flex items-center justify-center">
                      <Icon name="portfolio" size="lg" color="white" />
                    </div>
                    <h4 className="font-semibold text-white">My Bag</h4>
                  </Link>
                  <Link href="/settings" className="text-center p-6 bg-gray-700/50 border border-gray-600/50 rounded-lg cursor-pointer hover:border-green-500/60 hover:bg-gray-600/50 transition-all duration-200">
                    <div className="w-12 h-12 mx-auto mb-3 bg-gray-600 rounded-full flex items-center justify-center">
                      <Icon name="settings" size="lg" color="white" />
                    </div>
                    <h4 className="font-semibold text-white">Settings</h4>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
