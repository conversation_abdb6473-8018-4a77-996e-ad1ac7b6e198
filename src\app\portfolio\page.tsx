'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { LiveIndicator, PulseDot, BreathingCard, ShimmerText } from '@/components/ui/LiveIndicator';
import { FlameIcon } from '@/components/ui/FlameIcon';
import { PriceChart } from '@/components/charts/PriceChart';
import { useWallet } from '@/hooks/useWallet';
import { usePortfolio } from '@/hooks/useSupabase';
import { useTrading } from '@/hooks/useTrading';
import { usePriceMonitor } from '@/hooks/usePriceMonitor';
import { useStaggeredEntrance } from '@/hooks/useAnimations';
import { useRouter } from 'next/navigation';
import { formatCurrency, formatPercentage, formatSOL, getChangeColor, formatRelativeTime } from '@/lib/utils';
import { cn } from '@/lib/utils';
import {
  getDegenTerm,
  formatDegenAmount,
  formatDegenPercentage,
  getDegenMultiplier,
  getDegenStatus,
  DEGEN_EMOJIS
} from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';
import type { Position } from '@/types';

export default function PortfolioPage() {
  const { connected, balance } = useWallet();
  const { portfolio, loading, error, refetch } = usePortfolio();
  const { executeSellTrade, loading: tradingLoading, error: tradingError } = useTrading();
  const { getPriceHistory } = usePriceMonitor();
  const router = useRouter();
  const [selectedPosition, setSelectedPosition] = useState<Position | null>(null);
  const [isClient, setIsClient] = useState(false);
  const { containerRef, isVisible } = useStaggeredEntrance(4, 150);

  // Ensure client-side rendering for dynamic content
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Handle Jupiter DEX sell trades
  const handleSellTrade = async (position: Position, percentage: number) => {
    if (!connected) {
      alert('Please connect your wallet first');
      return;
    }

    try {
      const sellAmount = (position.amount * percentage) / 100;

      const tradeParams = {
        tokenAddress: position.tokenAddress,
        tokenSymbol: position.tokenSymbol,
        tokenName: position.tokenName,
        amount: sellAmount,
        slippage: 3, // 3% default slippage
        signalId: `portfolio-sell-${position.id}`,
      };

      console.log(`Executing ${percentage}% sell trade for ${position.tokenSymbol}:`, tradeParams);

      const result = await executeSellTrade(tradeParams);

      if (result.success) {
        alert(`${DEGEN_EMOJIS.money} Successfully sold ${percentage}% of your ${position.tokenSymbol} bag!\n\nTransaction: ${result.signature}`);
        // Refresh portfolio data
        refetch();
        // Close modal
        setSelectedPosition(null);
      } else {
        alert(`${DEGEN_EMOJIS.warning} Failed to sell ${position.tokenSymbol}: ${result.error}`);
      }
    } catch (error) {
      console.error('Error executing sell trade:', error);
      alert(`${DEGEN_EMOJIS.warning} Error executing trade: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Mock portfolio data for demonstration
  const mockPositions: Position[] = [
    {
      id: '1',
      portfolioId: 'portfolio1',
      tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      tokenSymbol: 'BONK',
      tokenName: 'Bonk',
      amount: 1000000,
      entryPrice: 0.000025,
      currentPrice: 0.000085,
      entryValue: 25,
      currentValue: 85,
      pnl: 60,
      pnlPercentage: 240,
      multiplier: 3.4,
      status: 'active',
      entryDate: new Date('2024-12-24T10:00:00Z'), // Static date - 2 days ago
      signalId: 'signal1',
    },
    {
      id: '2',
      portfolioId: 'portfolio1',
      tokenAddress: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
      tokenSymbol: 'SAMO',
      tokenName: 'Samoyedcoin',
      amount: 500,
      entryPrice: 0.02,
      currentPrice: 0.018,
      entryValue: 10,
      currentValue: 9,
      pnl: -1,
      pnlPercentage: -10,
      multiplier: 0.9,
      status: 'active',
      entryDate: new Date('2024-12-25T10:00:00Z'), // Static date - 1 day ago
      signalId: 'signal2',
    },
    {
      id: '3',
      portfolioId: 'portfolio1',
      tokenAddress: 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
      tokenSymbol: 'ORCA',
      tokenName: 'Orca',
      amount: 15,
      entryPrice: 3.45,
      currentPrice: 4.20,
      entryValue: 51.75,
      currentValue: 63,
      pnl: 11.25,
      pnlPercentage: 21.7,
      multiplier: 1.22,
      status: 'active',
      entryDate: new Date('2024-12-23T10:00:00Z'), // Static date - 3 days ago
      signalId: 'signal3',
    },
  ];

  // Use mock data for now
  const displayPositions = portfolio?.positions || mockPositions;
  const totalValue = displayPositions.reduce((sum, pos) => sum + pos.currentValue, 0);
  const totalPnL = displayPositions.reduce((sum, pos) => sum + pos.pnl, 0);
  const totalPnLPercentage = displayPositions.reduce((sum, pos) => sum + pos.entryValue, 0) > 0 
    ? (totalPnL / displayPositions.reduce((sum, pos) => sum + pos.entryValue, 0)) * 100 
    : 0;

  return (
    <MainLayout>
      <div className="space-y-6" ref={containerRef}>
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between slide-up-enter">
          <div>
            <ShimmerText speed="slow">
              <h1 className="text-4xl font-bold text-primary">
                My Bag
              </h1>
            </ShimmerText>
            <p className="mt-3 text-lg text-degen-green font-medium fade-in-enter">
              Track your diamond hands and moon missions
            </p>
            <div className="flex items-center gap-2 mt-2">
              <PulseDot color="green" size="sm" />
              <PulseDot color="gold" size="sm" />
              <PulseDot color="blue" size="sm" />
            </div>
          </div>

          <div className="mt-4 sm:mt-0 flex items-center space-x-3">
            <button
              onClick={refetch}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-bold transition-all duration-200 hover:shadow-lg hover:shadow-green-500/30 flex items-center gap-2"
            >
              <Icon name="refresh" size="sm" color="white" />
              Refresh
            </button>
          </div>
        </div>

        {!connected ? (
          <div className="bg-gray-900 border-2 border-red-500/60 rounded-2xl p-8 text-center hover:border-red-400 hover:shadow-lg hover:shadow-red-500/20 transition-all duration-300">
            <div className="text-6xl mb-4 animate-bounce">
              {DEGEN_EMOJIS.error}
            </div>
            <h3 className="text-xl font-bold text-white mb-2 flex items-center justify-center gap-2">
              {DEGEN_EMOJIS.chartDown} No Wallet Connected {DEGEN_EMOJIS.chartDown}
            </h3>
            <p className="text-gray-400">
              Connect your wallet to check your bag status {DEGEN_EMOJIS.money} Can't track gains without connection {DEGEN_EMOJIS.eyes}
            </p>
          </div>
        ) : (
          <>
            {/* Bag Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6" data-stagger>
              <BreathingCard intensity="subtle">
                <div className="bg-gray-900 border-2 border-green-500/60 rounded-2xl p-4 hover:border-green-400 hover:shadow-lg hover:shadow-green-500/20 transition-all duration-300 relative overflow-hidden group">
                  <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 relative z-10">
                    <div className="text-sm font-medium text-green-400 flex items-center gap-2 mb-2">
                      <PulseDot color="green" size="sm" />
                      {DEGEN_EMOJIS.money} Total Bag Value
                    </div>
                    <LiveIndicator
                      value={totalValue}
                      type="price"
                      className="text-2xl font-bold text-white"
                    >
                      {formatCurrency(totalValue)}
                    </LiveIndicator>
                    <p className={cn('text-sm mt-1 flex items-center gap-1', totalPnLPercentage >= 0 ? 'text-green-400' : 'text-red-400')}>
                      {totalPnLPercentage >= 0 ? '↗' : '↘'} {formatDegenPercentage(totalPnLPercentage)} ({formatCurrency(totalPnL)})
                    </p>
                  </div>
                </div>
              </BreathingCard>

              <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-4 hover:border-gray-600 hover:shadow-lg hover:shadow-gray-500/10 transition-all duration-300 relative overflow-hidden">
                <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 relative z-10">
                  <div className="text-sm font-medium text-gray-400 flex items-center gap-2 mb-2">
                    {DEGEN_EMOJIS.diamond} Active Bags
                  </div>
                  <div className="text-2xl font-bold text-white">
                    {displayPositions.filter(p => p.status === 'active').length}
                  </div>
                  <p className="text-sm text-green-400 mt-1 flex items-center gap-1">
                    {DEGEN_EMOJIS.rocket} {displayPositions.filter(p => p.pnl > 0).length} mooning
                  </p>
                </div>
              </div>

              <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-4 hover:border-green-500/60 hover:shadow-lg hover:shadow-green-500/10 transition-all duration-300 relative overflow-hidden">
                <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 relative z-10">
                  <div className="text-sm font-medium text-green-400 flex items-center gap-2 mb-2">
                    {DEGEN_EMOJIS.rocket} Best Moon Mission
                  </div>
                  {displayPositions.length > 0 && (
                    <>
                      <div className="text-lg font-bold text-white">
                        {displayPositions.reduce((best, pos) => pos.pnlPercentage > best.pnlPercentage ? pos : best).tokenSymbol}
                      </div>
                      <p className="text-sm text-green-400 mt-1 flex items-center gap-1">
                        ↗ {formatDegenPercentage(Math.max(...displayPositions.map(p => p.pnlPercentage)))}
                      </p>
                    </>
                  )}
                </div>
              </div>

              <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-4 hover:border-gray-600 hover:shadow-lg hover:shadow-gray-500/10 transition-all duration-300 relative overflow-hidden">
                <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 relative z-10">
                  <div className="text-sm font-medium text-gray-400 flex items-center gap-2 mb-2">
                    {DEGEN_EMOJIS.fire} Available SOL
                  </div>
                  <div className="text-2xl font-bold text-white">
                    {balance ? formatSOL(balance.sol) : 'Loading...'}
                  </div>
                  <p className="text-sm text-gray-400 mt-1 flex items-center gap-1">
                    {balance ? formatCurrency(balance.sol * 98.45) : ''} Ready
                  </p>
                </div>
              </div>
            </div>

            {/* Active Bags */}
            <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-6 hover:border-gray-600 hover:shadow-lg hover:shadow-gray-500/10 transition-all duration-300">
              <h2 className="text-xl font-bold text-white flex items-center gap-2 mb-6">
                {DEGEN_EMOJIS.diamond} Active Diamond Hands {DEGEN_EMOJIS.hands}
              </h2>

              {displayPositions.length === 0 ? (
                <div className="text-center py-8 text-gray-400">
                  <div className="text-6xl mb-4 animate-float">
                    {DEGEN_EMOJIS.chartDown}
                  </div>
                  <p className="text-white font-semibold">No bags yet, anon {DEGEN_EMOJIS.eyes}</p>
                  <p className="text-sm mt-1 text-gray-400">
                    Start aping into alpha to see your diamond hands here {DEGEN_EMOJIS.rocket}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {displayPositions.map((position) => (
                    <div
                      key={position.id}
                      className="bg-gray-800/80 backdrop-blur-sm border-2 border-gray-700/60 rounded-xl p-4 hover:border-green-500/60 hover:shadow-lg hover:shadow-green-500/20 cursor-pointer transition-all duration-300 relative group"
                      onClick={() => setSelectedPosition(position)}
                    >
                      {/* Header Section */}
                      <div className="flex items-center justify-between mb-4 relative pr-20">
                        <div className="flex items-center space-x-4">
                          {/* Token Icon */}
                          <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-lg shadow-lg">
                            {position.tokenSymbol.charAt(0)}
                          </div>

                          <div>
                            <h3 className="text-white text-xl font-bold tracking-tight">
                              {position.tokenSymbol}
                            </h3>
                            <p className="text-gray-400 text-sm">
                              {position.tokenName}
                            </p>
                          </div>

                          <div className={cn('px-3 py-1 rounded-full text-xs font-medium',
                            position.status === 'active' ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400')}>
                            {position.status === 'active' ? 'ACTIVE' : position.status.toUpperCase()}
                          </div>
                        </div>

                        <div className="text-right mr-16">
                          <div className="text-lg font-semibold text-white">
                            {formatCurrency(position.currentValue)}
                          </div>
                          <div className={cn('text-sm flex items-center gap-1', position.pnlPercentage >= 0 ? 'text-green-400' : 'text-red-400')}>
                            {position.pnlPercentage >= 0 ? '↗' : '↘'} {formatDegenPercentage(position.pnlPercentage)} ({formatCurrency(position.pnl)})
                          </div>
                        </div>

                        {/* Position Multiplier Badge - Fixed positioning to prevent overlaps */}
                        <div
                          className="absolute top-0 right-0 bg-gradient-to-r from-orange-500 to-red-500 text-white px-2.5 py-1 rounded-full text-xs font-bold flex items-center gap-1 z-40 shadow-lg min-w-[60px] justify-center"
                        >
                          <FlameIcon size="sm" color="white" />
                          {position.multiplier.toFixed(1)}X
                        </div>
                      </div>

                      {/* Market Data Grid */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-600/30">
                          <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">BAG SIZE</p>
                          <p className="text-white text-sm font-bold">{position.amount.toLocaleString()}</p>
                        </div>
                        <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-600/30">
                          <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">ENTRY</p>
                          <p className="text-white text-sm font-bold">{formatCurrency(position.entryPrice, 'USD', 6)}</p>
                        </div>
                        <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-600/30">
                          <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">CURRENT</p>
                          <p className="text-white text-sm font-bold">{formatCurrency(position.currentPrice, 'USD', 6)}</p>
                        </div>
                        <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-600/30">
                          <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">ENTRY DATE</p>
                          <p className="text-white text-sm font-bold">{formatRelativeTime(position.entryDate)}</p>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="space-y-3 mt-4">
                        {/* Chart Button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            if (typeof window !== 'undefined') {
                              const chartUrl = `https://dexscreener.com/solana/${position.tokenAddress}`;
                              window.open(chartUrl, '_blank', 'noopener,noreferrer');
                            }
                          }}
                          className="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-bold transition-all duration-200 hover:shadow-lg hover:shadow-green-500/30"
                        >
                          CHART
                        </button>

                        {/* Percentage-based Sell Buttons */}
                        <div className="grid grid-cols-4 gap-2">
                          {[25, 50, 75, 100].map((percentage) => (
                            <button
                              key={percentage}
                              onClick={(e) => {
                                e.stopPropagation();
                                // Check wallet connection before selling
                                if (!connected) {
                                  alert('Please connect your wallet first');
                                  return;
                                }
                                // Navigate to trading page with percentage-based sell
                                const params = new URLSearchParams({
                                  token: position.tokenAddress,
                                  symbol: position.tokenSymbol,
                                  action: 'sell',
                                  source: 'portfolio',
                                  percentage: percentage.toString(),
                                  amount: (position.amount * (percentage / 100)).toString()
                                });
                                router.push(`/trading?${params.toString()}`);
                              }}
                              className={`px-3 py-2 rounded-lg font-bold text-sm transition-all duration-200 hover:shadow-lg ${
                                percentage === 100
                                  ? 'bg-red-500 hover:bg-red-600 text-white hover:shadow-red-500/30'
                                  : 'bg-gray-600 hover:bg-gray-500 text-white'
                              }`}
                              title={`Sell ${percentage}% of position (${(position.amount * (percentage / 100)).toFixed(4)} ${position.tokenSymbol})`}
                            >
                              {percentage}%
                            </button>
                          ))}
                        </div>

                        {/* Custom Sell Button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            // Check wallet connection before selling
                            if (!connected) {
                              alert('Please connect your wallet first');
                              return;
                            }
                            // Navigate to trading page for custom sell amount
                            const params = new URLSearchParams({
                              token: position.tokenAddress,
                              symbol: position.tokenSymbol,
                              action: 'sell',
                              source: 'portfolio',
                              maxAmount: position.amount.toString()
                            });
                            router.push(`/trading?${params.toString()}`);
                          }}
                          className="w-full bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-lg font-bold text-sm transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/30"
                        >
                          CUSTOM SELL
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Bag Detail Modal */}
            {selectedPosition && (
              <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-sm">
                <div className="bg-gray-900 border-2 border-green-500/60 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto hover:shadow-lg hover:shadow-green-500/20">
                  <div className="p-6 border-b border-gray-700/50">
                    <div className="flex items-center justify-between">
                      <div>
                        <h2 className="text-xl font-semibold text-white flex items-center gap-2">
                          {DEGEN_EMOJIS.gem} {selectedPosition.tokenSymbol} Bag Details
                        </h2>
                        <p className="text-sm text-gray-400">
                          {selectedPosition.tokenName}
                        </p>
                      </div>
                      <button
                        onClick={() => setSelectedPosition(null)}
                        className="text-red-400 hover:text-white hover:bg-red-500/20 p-2 rounded-lg transition-all duration-200"
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-6 space-y-6">
                    {/* Bag Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-gray-800/80 backdrop-blur-sm border border-gray-700/30 rounded-lg">
                        <p className="text-sm text-gray-400 flex items-center justify-center gap-1 mb-2">
                          {DEGEN_EMOJIS.money} Current Bag Value
                        </p>
                        <p className="text-xl font-bold text-white">
                          {formatCurrency(selectedPosition.currentValue)}
                        </p>
                      </div>
                      <div className="text-center p-4 bg-gray-800/80 backdrop-blur-sm border border-gray-700/30 rounded-lg">
                        <p className="text-sm text-gray-400 flex items-center justify-center gap-1 mb-2">
                          {DEGEN_EMOJIS.chart} Gains/Rekt
                        </p>
                        <p className={cn('text-xl font-bold', selectedPosition.pnlPercentage >= 0 ? 'text-green-400' : 'text-red-400')}>
                          {formatCurrency(selectedPosition.pnl)}
                        </p>
                      </div>
                      <div className="text-center p-4 bg-gray-800/80 backdrop-blur-sm border border-gray-700/30 rounded-lg">
                        <p className="text-sm text-gray-400 flex items-center justify-center gap-1 mb-2">
                          {DEGEN_EMOJIS.rocket} Moon %
                        </p>
                        <p className={cn('text-xl font-bold', selectedPosition.pnlPercentage >= 0 ? 'text-green-400' : 'text-red-400')}>
                          {formatDegenPercentage(selectedPosition.pnlPercentage)}
                        </p>
                      </div>
                      <div className="text-center p-4 bg-gray-800/80 backdrop-blur-sm border border-gray-700/30 rounded-lg">
                        <p className="text-sm text-gray-400 flex items-center justify-center gap-1 mb-2">
                          {DEGEN_EMOJIS.diamond} Multiplier
                        </p>
                        <p className="text-xl font-bold text-white">
                          {selectedPosition.multiplier.toFixed(1)}X
                        </p>
                      </div>
                    </div>

                    {/* Price Chart */}
                    <PriceChart
                      tokenAddress={selectedPosition.tokenAddress}
                      tokenSymbol={selectedPosition.tokenSymbol}
                      priceHistory={getPriceHistory(selectedPosition.tokenAddress)}
                      currentPrice={selectedPosition.currentPrice}
                      height={300}
                    />

                    {/* Trading Error Display */}
                    {tradingError && (
                      <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 mb-4">
                        <p className="text-red-400 text-sm font-medium">
                          {DEGEN_EMOJIS.warning} Trading Error: {tradingError}
                        </p>
                      </div>
                    )}

                    {/* Bag Actions */}
                    <div className="grid grid-cols-2 gap-3 mb-3">
                      <button
                        className={`bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg font-bold transition-all duration-200 hover:shadow-lg hover:shadow-green-500/30 ${tradingLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => handleSellTrade(selectedPosition, 25)}
                        disabled={tradingLoading}
                      >
                        {tradingLoading ? 'SELLING...' : 'SELL 25%'}
                      </button>
                      <button
                        className={`bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg font-bold transition-all duration-200 hover:shadow-lg hover:shadow-green-500/30 ${tradingLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => handleSellTrade(selectedPosition, 50)}
                        disabled={tradingLoading}
                      >
                        {tradingLoading ? 'SELLING...' : 'SELL 50%'}
                      </button>
                      <button
                        className={`bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded-lg font-bold transition-all duration-200 hover:shadow-lg hover:shadow-orange-500/30 ${tradingLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => handleSellTrade(selectedPosition, 75)}
                        disabled={tradingLoading}
                      >
                        {tradingLoading ? 'SELLING...' : 'SELL 75%'}
                      </button>
                      <button
                        className={`bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-lg font-bold transition-all duration-200 hover:shadow-lg hover:shadow-red-500/30 ${tradingLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => {
                          const confirmed = confirm(`${DEGEN_EMOJIS.warning} Are you sure you want to exit your entire ${selectedPosition.tokenSymbol} bag?`);
                          if (confirmed) {
                            handleSellTrade(selectedPosition, 100);
                          }
                        }}
                        disabled={tradingLoading}
                      >
                        {tradingLoading ? 'EXITING...' : 'EXIT ALL'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </MainLayout>
  );
}
