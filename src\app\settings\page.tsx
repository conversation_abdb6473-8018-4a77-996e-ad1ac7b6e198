'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { Modal } from '@/components/ui/Modal';
import { ShimmerText } from '@/components/ui/LiveIndicator';

import { WalletDebugPanel } from '@/components/wallet/WalletDebugPanel';
import { useWallet } from '@/hooks/useWallet';
import { useSupabaseAuth } from '@/hooks/useSupabase';
import { useAutoTrader } from '@/hooks/useTrading';
import { usePriceMonitor } from '@/hooks/usePriceMonitor';
import { formatSOL } from '@/lib/utils';
import { SignalService } from '@/lib/supabase/services/signalService';
import { telegramUserClient } from '@/lib/telegram/userAccountClient';
import type { TelegramChannel } from '@/types';

export default function SettingsPage() {
  const { connected, balance, linkWalletAddress, publicKey } = useWallet();
  const { userProfile, signOut } = useSupabaseAuth();
  const { settings: autoTraderSettings, updateSettings: updateAutoTraderSettings } = useAutoTrader();
  const { status: priceMonitorStatus } = usePriceMonitor();

  const [activeTab, setActiveTab] = useState('trading');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingAction, setPendingAction] = useState<string | null>(null);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');

  // Telegram authentication state
  const [telegramAuth, setTelegramAuth] = useState({
    phoneNumber: '',
    verificationCode: '',
    isPhoneSubmitted: false,
    isVerifying: false,
    isAuthenticated: false,
    selectedChannels: [] as string[],
    availableChannels: [] as any[],
    showChannelSelection: false,
  });

  // Telegram channels state
  const [telegramChannels, setTelegramChannels] = useState<TelegramChannel[]>([]);
  const [newChannelInput, setNewChannelInput] = useState('');
  const [isLoadingChannels, setIsLoadingChannels] = useState(false);
  const [isAddingChannel, setIsAddingChannel] = useState(false);

  // Local state for form values
  const [tradingSettings, setTradingSettings] = useState({
    defaultAmount: 0.1,
    maxPositionPercentage: 5,
    stopLossPercentage: 30,
    slippageTolerance: 3,
    autoTrade: false,
    maxActivePositions: 10,
    profitTakingEnabled: true,
  });

  // Load settings from auto trader
  useEffect(() => {
    if (autoTraderSettings) {
      setTradingSettings({
        defaultAmount: autoTraderSettings.defaultAmount,
        maxPositionPercentage: autoTraderSettings.maxPositionPercentage,
        stopLossPercentage: autoTraderSettings.stopLossPercentage,
        slippageTolerance: 3, // Not in auto trader settings
        autoTrade: autoTraderSettings.enabled,
        maxActivePositions: 10, // Not in auto trader settings
        profitTakingEnabled: autoTraderSettings.profitTakingEnabled,
      });
    }
  }, [autoTraderSettings]);

  const loadTelegramChannels = useCallback(async () => {
    setIsLoadingChannels(true);
    try {
      const channels = await SignalService.getAllChannels();
      setTelegramChannels(channels);
    } catch (error) {
      console.error('Error loading Telegram channels:', error);
      setNotificationMessage('Failed to load Telegram channels');
      setShowSuccessNotification(true);
      setTimeout(() => setShowSuccessNotification(false), 3000);
    } finally {
      setIsLoadingChannels(false);
    }
  }, []);

  // Load Telegram channels
  useEffect(() => {
    loadTelegramChannels();
  }, [loadTelegramChannels]);

  const tabs = [
    { id: 'account', name: 'Account', icon: '👤' },
    { id: 'trading', name: 'Trading', icon: '🤖' },
    { id: 'signals', name: 'Signals', icon: '📡' },
    { id: 'notifications', name: 'Notifications', icon: '🔔' },
    { id: 'security', name: 'Security', icon: '🔒' },
    // Developer tab for wallet debugging
    { id: 'developer', name: 'Developer', icon: '⚙️' },
  ];

  const handleSaveTradingSettings = () => {
    updateAutoTraderSettings({
      enabled: tradingSettings.autoTrade,
      defaultAmount: tradingSettings.defaultAmount,
      maxPositionPercentage: tradingSettings.maxPositionPercentage,
      stopLossPercentage: tradingSettings.stopLossPercentage,
      profitTakingEnabled: tradingSettings.profitTakingEnabled,
    });
    setNotificationMessage('Trading settings saved successfully! 🚀');
    setShowSuccessNotification(true);
    setTimeout(() => setShowSuccessNotification(false), 3000);
  };

  const handleLinkWallet = async () => {
    if (publicKey) {
      await linkWalletAddress(publicKey.toString());
      alert('Wallet linked successfully!');
    }
  };

  const handleAddTelegramChannel = async () => {
    if (!newChannelInput.trim()) {
      setNotificationMessage('Please enter a channel username or ID');
      setShowSuccessNotification(true);
      setTimeout(() => setShowSuccessNotification(false), 3000);
      return;
    }

    setIsAddingChannel(true);
    try {
      // Clean the input (remove @ if present)
      const cleanUsername = newChannelInput.trim().replace(/^@/, '');

      // Create new channel object
      const newChannel = {
        name: cleanUsername,
        username: cleanUsername,
        description: `Added via Settings - ${new Date().toLocaleDateString()}`,
        memberCount: 0,
        active: true,
      };

      const addedChannel = await SignalService.addChannel(newChannel);

      if (addedChannel) {
        setTelegramChannels(prev => [...prev, addedChannel]);
        setNewChannelInput('');
        setNotificationMessage(`Successfully added channel @${cleanUsername}! 🎉`);
      } else {
        setNotificationMessage('Failed to add channel. Please check the username and try again.');
      }
    } catch (error) {
      console.error('Error adding channel:', error);
      setNotificationMessage('Error adding channel. Please try again.');
    } finally {
      setIsAddingChannel(false);
      setShowSuccessNotification(true);
      setTimeout(() => setShowSuccessNotification(false), 3000);
    }
  };

  const handleToggleChannel = async (channelId: string, currentActive: boolean) => {
    try {
      const success = await SignalService.updateChannelStatus(channelId, !currentActive);

      if (success) {
        setTelegramChannels(prev =>
          prev.map(channel =>
            channel.id === channelId
              ? { ...channel, active: !currentActive }
              : channel
          )
        );
        setNotificationMessage(`Channel ${!currentActive ? 'activated' : 'deactivated'} successfully! ✅`);
      } else {
        setNotificationMessage('Failed to update channel status');
      }
    } catch (error) {
      console.error('Error toggling channel:', error);
      setNotificationMessage('Error updating channel. Please try again.');
    } finally {
      setShowSuccessNotification(true);
      setTimeout(() => setShowSuccessNotification(false), 3000);
    }
  };

  const handleRemoveChannel = async (channelId: string, channelName: string) => {
    if (!confirm(`Are you sure you want to remove the channel "${channelName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const success = await SignalService.deleteChannel(channelId);

      if (success) {
        setTelegramChannels(prev => prev.filter(channel => channel.id !== channelId));
        setNotificationMessage(`Channel "${channelName}" removed successfully! 🗑️`);
      } else {
        setNotificationMessage('Failed to remove channel');
      }
    } catch (error) {
      console.error('Error removing channel:', error);
      setNotificationMessage('Error removing channel. Please try again.');
    } finally {
      setShowSuccessNotification(true);
      setTimeout(() => setShowSuccessNotification(false), 3000);
    }
  };

  const handleConfirmAction = (action: string) => {
    setPendingAction(action);
    setShowConfirmModal(true);
  };

  const executeAction = async () => {
    if (pendingAction === 'signOut') {
      await signOut();
    }
    setShowConfirmModal(false);
    setPendingAction(null);
  };

  // Telegram authentication handlers
  const handlePhoneSubmit = useCallback(async () => {
    if (!telegramAuth.phoneNumber.trim()) return;

    setTelegramAuth(prev => ({ ...prev, isVerifying: true }));

    try {
      const result = await telegramUserClient.sendVerificationCode(telegramAuth.phoneNumber);

      if (result.success) {
        setTelegramAuth(prev => ({
          ...prev,
          isPhoneSubmitted: true,
          isVerifying: false
        }));

        setNotificationMessage('Verification code sent to your phone');
        setShowSuccessNotification(true);
      } else {
        setTelegramAuth(prev => ({ ...prev, isVerifying: false }));
        setNotificationMessage(result.error || 'Failed to send verification code');
        setShowSuccessNotification(false);
      }
    } catch (error) {
      console.error('Failed to send verification code:', error);
      setTelegramAuth(prev => ({ ...prev, isVerifying: false }));
      setNotificationMessage('Failed to send verification code. Please try again.');
      setShowSuccessNotification(false);
    }
  }, [telegramAuth.phoneNumber]);

  const handleVerificationSubmit = useCallback(async () => {
    if (!telegramAuth.verificationCode.trim()) return;

    setTelegramAuth(prev => ({ ...prev, isVerifying: true }));

    try {
      const result = await telegramUserClient.verifyCode(telegramAuth.phoneNumber, telegramAuth.verificationCode);

      if (result.success && result.channels) {
        setTelegramAuth(prev => ({
          ...prev,
          isAuthenticated: true,
          isVerifying: false,
          showChannelSelection: true,
          availableChannels: result.channels
        }));

        setNotificationMessage('Successfully authenticated with Telegram');
        setShowSuccessNotification(true);
      } else {
        setTelegramAuth(prev => ({ ...prev, isVerifying: false }));
        setNotificationMessage(result.error || 'Failed to verify code');
        setShowSuccessNotification(false);
      }
    } catch (error) {
      console.error('Failed to verify code:', error);
      setTelegramAuth(prev => ({ ...prev, isVerifying: false }));
      setNotificationMessage('Failed to verify code. Please try again.');
      setShowSuccessNotification(false);
    }
  }, [telegramAuth.verificationCode, telegramAuth.phoneNumber]);

  const handleChannelToggle = useCallback((channelId: string) => {
    setTelegramAuth(prev => ({
      ...prev,
      selectedChannels: prev.selectedChannels.includes(channelId)
        ? prev.selectedChannels.filter(id => id !== channelId)
        : [...prev.selectedChannels, channelId]
    }));
  }, []);

  const handleSaveChannels = useCallback(async () => {
    try {
      const success = await telegramUserClient.saveSelectedChannels(telegramAuth.selectedChannels);

      if (success) {
        setNotificationMessage(`Successfully configured ${telegramAuth.selectedChannels.length} channels for monitoring`);
        setShowSuccessNotification(true);
      } else {
        setNotificationMessage('Failed to save channel configuration');
        setShowSuccessNotification(false);
      }
    } catch (error) {
      console.error('Failed to save channels:', error);
      setNotificationMessage('Failed to save channel configuration');
      setShowSuccessNotification(false);
    }
  }, [telegramAuth.selectedChannels]);

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="slide-up-enter">
          <ShimmerText speed="slow">
            <h1 className="text-4xl font-bold text-primary">
              Settings
            </h1>
          </ShimmerText>
          <p className="mt-3 text-lg text-degen-purple font-medium fade-in-enter">
            Configure your trading preferences and application settings
          </p>
        </div>

        {/* Tabs */}
        <div className="border-b border-subtle">
          <nav className="-mb-px flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors
                  ${activeTab === tab.id
                    ? 'border-neutral-400 text-primary'
                    : 'border-transparent text-muted hover:text-secondary hover:border-neutral-600'
                  }
                `}
              >
                <span>{tab.icon}</span>
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'account' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Account Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email
                      </label>
                      <Input
                        value={userProfile?.email || 'Not set'}
                        disabled
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Wallet Address
                      </label>
                      <div className="flex space-x-2">
                        <Input
                          value={userProfile?.walletAddress || publicKey?.toString() || 'Not connected'}
                          disabled
                          className="flex-1"
                        />
                        {connected && !userProfile?.walletAddress && (
                          <Button onClick={handleLinkWallet} size="sm">
                            Link
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                      Account Status
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span className="text-sm font-medium">Wallet Connected</span>
                        <Badge variant={connected ? 'success' : 'error'}>
                          {connected ? 'Connected' : 'Disconnected'}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span className="text-sm font-medium">SOL Balance</span>
                        <span className="text-sm font-medium">
                          {balance ? formatSOL(balance.sol) : 'N/A'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span className="text-sm font-medium">Auto Trading</span>
                        <Badge variant={autoTraderSettings?.enabled ? 'success' : 'secondary'}>
                          {autoTraderSettings?.enabled ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                      Danger Zone
                    </h4>
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="text-sm font-medium text-red-800 dark:text-red-400">
                            Sign Out
                          </h5>
                          <p className="text-xs text-red-700 dark:text-red-300 mt-1">
                            This will disconnect your wallet and clear your session
                          </p>
                        </div>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleConfirmAction('signOut')}
                        >
                          Sign Out
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'trading' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Trading Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Default Trade Amount (SOL)"
                      type="number"
                      step="0.01"
                      value={tradingSettings.defaultAmount}
                      onChange={(e) => setTradingSettings(prev => ({ ...prev, defaultAmount: parseFloat(e.target.value) || 0 }))}
                      helperText="Amount to trade per signal"
                    />
                    <Input
                      label="Max Position Percentage (%)"
                      type="number"
                      step="0.1"
                      value={tradingSettings.maxPositionPercentage}
                      onChange={(e) => setTradingSettings(prev => ({ ...prev, maxPositionPercentage: parseFloat(e.target.value) || 0 }))}
                      helperText="Maximum % of portfolio per position"
                    />
                    <Input
                      label="Stop Loss Percentage (%)"
                      type="number"
                      step="0.1"
                      value={tradingSettings.stopLossPercentage}
                      onChange={(e) => setTradingSettings(prev => ({ ...prev, stopLossPercentage: parseFloat(e.target.value) || 0 }))}
                      helperText="Automatic stop loss threshold"
                    />
                    <Input
                      label="Slippage Tolerance (%)"
                      type="number"
                      step="0.1"
                      value={tradingSettings.slippageTolerance}
                      onChange={(e) => setTradingSettings(prev => ({ ...prev, slippageTolerance: parseFloat(e.target.value) || 0 }))}
                      helperText="Maximum acceptable slippage"
                    />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="autoTrade"
                        checked={tradingSettings.autoTrade}
                        onChange={(e) => setTradingSettings(prev => ({ ...prev, autoTrade: e.target.checked }))}
                        className="rounded border-neutral-600 text-neutral-400 focus:ring-neutral-500"
                      />
                      <label htmlFor="autoTrade" className="text-sm text-secondary">
                        Enable automatic trading
                      </label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="profitTaking"
                        checked={tradingSettings.profitTakingEnabled}
                        onChange={(e) => setTradingSettings(prev => ({ ...prev, profitTakingEnabled: e.target.checked }))}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="profitTaking" className="text-sm text-gray-700 dark:text-gray-300">
                        Enable profit taking (2x, 3x, 5x levels)
                      </label>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                      System Status
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span className="text-sm font-medium">Price Monitor</span>
                        <Badge variant={priceMonitorStatus.isRunning ? 'success' : 'secondary'}>
                          {priceMonitorStatus.isRunning ? 'Running' : 'Stopped'}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span className="text-sm font-medium">Tracked Tokens</span>
                        <span className="text-sm font-medium">
                          {priceMonitorStatus.trackedTokensCount}
                        </span>
                      </div>
                    </div>
                  </div>

                  <Button onClick={handleSaveTradingSettings}>Save Trading Settings</Button>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'signals' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Telegram Channels</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Development Notice */}
                  {process.env.NEXT_PUBLIC_SUPABASE_URL?.includes('mock-project-id') && (
                    <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-4">
                      <div className="flex items-center gap-2 text-yellow-400 text-sm">
                        <span>⚠️</span>
                        <span className="font-medium">Development Mode</span>
                      </div>
                      <p className="text-yellow-300/80 text-xs mt-1">
                        Using demo Supabase configuration. Replace with real credentials in .env.local for production.
                      </p>
                    </div>
                  )}

                  {isLoadingChannels ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-degen-green"></div>
                      <span className="ml-2 text-degen-gray">Loading channels...</span>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {telegramChannels.length === 0 ? (
                        <div className="text-center py-8 text-degen-gray">
                          <p className="text-lg">📡 No Telegram channels configured</p>
                          <p className="text-sm mt-2">Add your first channel below to start monitoring signals</p>
                        </div>
                      ) : (
                        telegramChannels.map((channel) => (
                          <div key={channel.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div className="flex-1">
                              <p className="font-medium">{channel.name}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">@{channel.username}</p>
                              {channel.signalCount > 0 && (
                                <p className="text-xs text-degen-green mt-1">{channel.signalCount} signals processed</p>
                              )}
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant={channel.active ? "success" : "secondary"}>
                                {channel.active ? "Active" : "Inactive"}
                              </Badge>
                              <Button
                                size="sm"
                                variant={channel.active ? "secondary" : "default"}
                                onClick={() => handleToggleChannel(channel.id, channel.active)}
                              >
                                {channel.active ? "Disable" : "Enable"}
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => handleRemoveChannel(channel.id, channel.name)}
                              >
                                Remove
                              </Button>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  )}

                  <div className="flex space-x-2">
                    <Input
                      placeholder="Channel username or ID (e.g., @cryptosignals)"
                      className="flex-1"
                      value={newChannelInput}
                      onChange={(e) => setNewChannelInput(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          handleAddTelegramChannel();
                        }
                      }}
                    />
                    <Button
                      onClick={handleAddTelegramChannel}
                      disabled={isAddingChannel || !newChannelInput.trim()}
                    >
                      {isAddingChannel ? "Adding..." : "Add Channel"}
                    </Button>
                  </div>

                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    💡 Tip: Enter the channel username (with or without @) or the channel ID. Your connected Telegram account will monitor these channels for trading signals.
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Signal Filters</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Input
                    label="Minimum Market Cap"
                    type="number"
                    placeholder="1000000"
                    helperText="Minimum market cap for valid signals"
                  />
                  <Input
                    label="Maximum Market Cap"
                    type="number"
                    placeholder="**********"
                    helperText="Maximum market cap for valid signals"
                  />
                  <Input
                    label="Minimum Confidence (%)"
                    type="number"
                    step="0.1"
                    defaultValue="60"
                    helperText="Minimum confidence score for signal processing"
                  />
                  <Button>Save Signal Settings</Button>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Notification Preferences</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {[
                      { id: 'signals', label: 'New Signals', description: 'Get notified when new signals are detected' },
                      { id: 'trades', label: 'Trade Execution', description: 'Notifications for completed trades' },
                      { id: 'portfolio', label: 'Portfolio Updates', description: 'Daily portfolio performance summaries' },
                      { id: 'alerts', label: 'Price Alerts', description: 'Notifications for significant price movements' },
                    ].map((item) => (
                      <div key={item.id} className="flex items-start space-x-3">
                        <input
                          type="checkbox"
                          id={item.id}
                          defaultChecked
                          className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <div className="flex-1">
                          <label htmlFor={item.id} className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {item.label}
                          </label>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {item.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button>Save Notification Settings</Button>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Wallet Security</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-400">
                          Security Notice
                        </h3>
                        <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                          Signal V1 never stores your private keys. All transactions are signed by your connected wallet.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Wallet Connected</span>
                      <Badge variant={connected ? 'success' : 'error'}>
                        {connected ? 'Connected' : 'Disconnected'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Auto-approve trades</span>
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Require confirmation for large trades</span>
                      <input
                        type="checkbox"
                        defaultChecked
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'developer' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Telegram User Account Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Development Mode Notice */}
                  {process.env.NODE_ENV === 'development' && !telegramUserClient.isConfigured() && (
                    <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                      <div className="flex items-center gap-2 text-yellow-400 text-sm">
                        <span>⚠️</span>
                        <span className="font-medium">Development Mode</span>
                      </div>
                      <p className="text-yellow-300/80 text-xs mt-1">
                        Telegram API credentials not configured. Using mock verification for development.
                        Set TELEGRAM_API_ID and TELEGRAM_API_HASH environment variables for production.
                      </p>
                    </div>
                  )}

                  {/* User Account Authentication Flow */}
                  {!telegramAuth.isAuthenticated ? (
                    <div className="space-y-4">
                      {/* Phone Number Step */}
                      {!telegramAuth.isPhoneSubmitted ? (
                        <div className="space-y-4">
                          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                            <div className="flex items-center gap-2 text-green-600 dark:text-green-400 text-sm">
                              <span>📱</span>
                              <span className="font-medium">User Account Authentication</span>
                            </div>
                            <p className="text-green-700 dark:text-green-300 text-xs mt-1">
                              Connect your personal Telegram account to monitor channels and groups
                            </p>
                          </div>

                          <Input
                            label="Phone Number"
                            value={telegramAuth.phoneNumber}
                            onChange={(e) => setTelegramAuth(prev => ({ ...prev, phoneNumber: e.target.value }))}
                            placeholder="+**********"
                            helperText="Enter your Telegram account phone number"
                          />

                          <Button
                            onClick={handlePhoneSubmit}
                            disabled={!telegramAuth.phoneNumber.trim() || telegramAuth.isVerifying}
                            className="w-full"
                          >
                            {telegramAuth.isVerifying ? 'Sending Code...' : 'Send Verification Code'}
                          </Button>
                        </div>
                      ) : (
                        /* Verification Code Step */
                        <div className="space-y-4">
                          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                            <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400 text-sm">
                              <span>🔐</span>
                              <span className="font-medium">Verification Required</span>
                            </div>
                            <p className="text-blue-700 dark:text-blue-300 text-xs mt-1">
                              Enter the verification code sent to {telegramAuth.phoneNumber}
                            </p>
                          </div>

                          <Input
                            label="Verification Code"
                            value={telegramAuth.verificationCode}
                            onChange={(e) => setTelegramAuth(prev => ({ ...prev, verificationCode: e.target.value }))}
                            placeholder="123456"
                            helperText="6-digit code from Telegram"
                            maxLength={6}
                          />

                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              onClick={() => setTelegramAuth(prev => ({
                                ...prev,
                                isPhoneSubmitted: false,
                                verificationCode: ''
                              }))}
                              className="flex-1"
                            >
                              Back
                            </Button>
                            <Button
                              onClick={handleVerificationSubmit}
                              disabled={!telegramAuth.verificationCode.trim() || telegramAuth.isVerifying}
                              className="flex-1"
                            >
                              {telegramAuth.isVerifying ? 'Verifying...' : 'Verify Code'}
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    /* Authenticated State - Channel Selection */
                    <div className="space-y-4">
                      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 text-sm">
                          <span>✅</span>
                          <span className="font-medium">Successfully Authenticated</span>
                        </div>
                        <p className="text-green-700 dark:text-green-300 text-xs mt-1">
                          Connected to {telegramAuth.phoneNumber} • Select channels to monitor
                        </p>
                      </div>

                      {telegramAuth.showChannelSelection && (
                        <div className="space-y-4">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                            Available Channels & Groups
                          </h4>

                          <div className="space-y-2 max-h-64 overflow-y-auto">
                            {telegramAuth.availableChannels.map((channel) => (
                              <div
                                key={channel.id}
                                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
                              >
                                <div className="flex-1">
                                  <div className="font-medium text-gray-900 dark:text-white text-sm">
                                    {channel.name}
                                  </div>
                                  <div className="text-xs text-gray-500 dark:text-gray-400">
                                    {channel.username} • {channel.memberCount.toLocaleString()} members
                                  </div>
                                </div>
                                <label className="flex items-center">
                                  <input
                                    type="checkbox"
                                    checked={telegramAuth.selectedChannels.includes(channel.id)}
                                    onChange={() => handleChannelToggle(channel.id)}
                                    className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 dark:focus:ring-green-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                  />
                                </label>
                              </div>
                            ))}
                          </div>

                          <Button
                            onClick={handleSaveChannels}
                            disabled={telegramAuth.selectedChannels.length === 0}
                            className="w-full"
                          >
                            Save Selected Channels ({telegramAuth.selectedChannels.length})
                          </Button>
                        </div>
                      )}

                      <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                        <Button
                          variant="outline"
                          onClick={() => {
                            telegramUserClient.reset();
                            setTelegramAuth({
                              phoneNumber: '',
                              verificationCode: '',
                              isPhoneSubmitted: false,
                              isVerifying: false,
                              isAuthenticated: false,
                              selectedChannels: [],
                              availableChannels: [],
                              showChannelSelection: false,
                            });
                          }}
                          className="w-full"
                        >
                          Reset Authentication
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Solana RPC Configuration */}
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <Input
                      label="Solana RPC URL"
                      placeholder="https://api.mainnet-beta.solana.com"
                      helperText="Custom RPC endpoint (optional)"
                    />
                  </div>
                </CardContent>
              </Card>


            </div>
          )}

          {activeTab === 'developer' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Wallet Connection Debug</CardTitle>
                </CardHeader>
                <CardContent>
                  <WalletDebugPanel />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Development Tools</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-400">
                          Development Mode
                        </h3>
                        <p className="mt-1 text-sm text-blue-700 dark:text-blue-300">
                          This tab is for debugging wallet connection issues and testing Signal V1 functionality.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Confirmation Modal */}
        <Modal
          isOpen={showConfirmModal}
          onClose={() => setShowConfirmModal(false)}
          title="Confirm Action"
          size="sm"
        >
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-400">
              {pendingAction === 'signOut' && 'Are you sure you want to sign out? This will disconnect your wallet and clear your session.'}
            </p>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowConfirmModal(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={executeAction}
                className="flex-1"
              >
                Confirm
              </Button>
            </div>
          </div>
        </Modal>

        {/* Success Notification */}
        {showSuccessNotification && (
          <div className="fixed top-4 right-4 z-50 animate-slide-in-right">
            <div className="bg-gradient-profit border border-degen-green/30 rounded-lg p-4 shadow-lg glow-green max-w-sm">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="flex-1">
                  <p className="text-white font-medium text-sm">
                    {notificationMessage}
                  </p>
                </div>
                <button
                  onClick={() => setShowSuccessNotification(false)}
                  className="flex-shrink-0 text-white/80 hover:text-white transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
