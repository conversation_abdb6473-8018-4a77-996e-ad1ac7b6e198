'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LiveIndicator, PulseDot, BreathingCard, ShimmerText } from '@/components/ui/LiveIndicator';
import { FlameIcon } from '@/components/ui/FlameIcon';
import { TokenCard } from '@/components/trading/TokenCard';
import { useSignals } from '@/hooks/useSupabase';
import { useStaggeredEntrance } from '@/hooks/useAnimations';
import { formatRelativeTime, formatCurrency } from '@/lib/utils';
import {
  getDegenTerm,
  formatDegenAmount,
  DEGEN_EMOJIS
} from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';
import type { TelegramSignal } from '@/types';

export default function SignalsPage() {
  const { signals, loading, error, refetch } = useSignals(100);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterValid, setFilterValid] = useState<boolean | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);
  const { containerRef, isVisible } = useStaggeredEntrance(6, 100);

  // Mock data for demonstration since we don't have real Telegram integration yet
  const mockSignals: TelegramSignal[] = [
    {
      id: '1',
      channelId: 'channel1',
      channelName: 'Crypto Signals Pro',
      messageId: 12345,
      content: '🚨Alerted at $BONK - CA: DezXAZ8z...B1pPB263 - Market Cap: $1.5B - This could be a gem! 🚀',
      tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      tokenSymbol: 'BONK',
      marketCap: 1500000000,
      images: [],
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      processed: true,
      valid: true,
      error: null,
    },
    {
      id: '2',
      channelId: 'channel2',
      channelName: 'Solana Gems',
      messageId: 12346,
      content: '🛎️First Hit on $SAMO - Contract: 7xKXtg2C...TZRuJosgAsU - MC: $50M - Early entry opportunity!',
      tokenAddress: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
      tokenSymbol: 'SAMO',
      marketCap: 50000000,
      images: [],
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      processed: true,
      valid: true,
      error: null,
    },
    {
      id: '3',
      channelId: 'channel1',
      channelName: 'Crypto Signals Pro',
      messageId: 12347,
      content: 'SIGNAL: $ORCA - Address: orcaEKTd...U1kektZE - Price: $3.45 - Target: $10 🎯',
      tokenAddress: 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
      tokenSymbol: 'ORCA',
      marketCap: 800000000,
      images: [],
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      processed: true,
      valid: true,
      error: null,
    },
  ];

  // Use mock data for now
  const displaySignals = signals.length > 0 ? signals : mockSignals;

  // Filter signals based on search and filters
  const filteredSignals = displaySignals.filter(signal => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!signal.tokenSymbol?.toLowerCase().includes(query) &&
          !signal.tokenAddress.toLowerCase().includes(query) &&
          !signal.channelName.toLowerCase().includes(query)) {
        return false;
      }
    }

    if (filterValid !== null && signal.valid !== filterValid) {
      return false;
    }

    if (selectedChannel && signal.channelId !== selectedChannel) {
      return false;
    }

    return true;
  });

  // Get unique channels
  const channels = Array.from(new Set(displaySignals.map(s => s.channelName)))
    .map(name => ({
      id: displaySignals.find(s => s.channelName === name)?.channelId || '',
      name,
    }));

  return (
    <MainLayout>
      <div className="space-y-6" ref={containerRef}>
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between slide-up-enter">
          <div>
            <ShimmerText speed="slow">
              <h1 className="text-4xl font-bold text-primary">
                Live Alpha Signals
              </h1>
            </ShimmerText>
            <p className="mt-3 text-lg text-degen-purple font-medium fade-in-enter">
              Real-time alpha from the trenches - Fresh signals incoming
            </p>
            <div className="flex items-center gap-2 mt-2">
              <PulseDot color="purple" size="sm" />
              <PulseDot color="blue" size="sm" />
              <PulseDot color="green" size="sm" />
            </div>
          </div>

          <div className="mt-4 sm:mt-0 flex items-center space-x-3">
            <BreathingCard intensity="strong">
              <Badge variant="alpha" className="animate-pulse glow-purple border-gradient-purple glow-pulse-effect">
                <Icon name="signals" size="sm" color="white" className="mr-1" />
                LIVE ALPHA
              </Badge>
            </BreathingCard>
            <BreathingCard intensity="normal">
              <Button onClick={refetch} size="sm" variant="diamond" className="font-bold glow-purple">
                <Icon name="refresh" size="sm" color="white" className="mr-1" />
                Refresh
              </Button>
            </BreathingCard>
          </div>
        </div>

        {/* Alpha Filters */}
        <Card className="bg-gradient-sophisticated border-gradient-purple">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <Input
                placeholder="Search alpha signals..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-gradient-neutral-subtle border-gradient-blue text-degen-blue placeholder:text-degen-gray"
                leftIcon={
                  <svg className="w-4 h-4 text-degen-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                }
              />

              {/* Channel Filter */}
              <select
                className="h-10 w-full rounded-md bg-gradient-signal-subtle border-gradient-purple px-3 py-2 text-sm text-degen-purple font-medium"
                value={selectedChannel || ''}
                onChange={(e) => setSelectedChannel(e.target.value || null)}
              >
                <option value="">All Alpha Sources</option>
                {channels.map(channel => (
                  <option key={channel.id} value={channel.id}>
                    {channel.name}
                  </option>
                ))}
              </select>

              {/* Valid Filter */}
              <select
                className="h-10 w-full rounded-md bg-gradient-profit-subtle border-gradient-green px-3 py-2 text-sm text-degen-green font-medium"
                value={filterValid === null ? '' : filterValid.toString()}
                onChange={(e) => setFilterValid(e.target.value === '' ? null : e.target.value === 'true')}
              >
                <option value="">All Alpha</option>
                <option value="true">Valid Alpha Only</option>
                <option value="false">Sus Signals</option>
              </select>

              {/* Results Count */}
              <div className="flex items-center text-sm text-degen-gold font-bold bg-gradient-premium-subtle p-3 rounded-lg">
                {filteredSignals.length} of {displaySignals.length} signals
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Signals List */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <Card className="bg-gradient-loss border-2 border-degen-red/50 glow-red">
            <CardContent className="p-8 text-center">
              <div className="text-6xl mb-4 animate-bounce">
                {DEGEN_EMOJIS.error}
              </div>
              <h3 className="text-xl font-bold text-white mb-2 flex items-center justify-center gap-2">
                {DEGEN_EMOJIS.chartDown} Alpha Feed Rekt {DEGEN_EMOJIS.chartDown}
              </h3>
              <p className="text-white mb-4">
                Something went wrong loading the alpha: {error}
              </p>
              <Button onClick={refetch} variant="moon" className="font-bold">
                {DEGEN_EMOJIS.target} Try Again
              </Button>
            </CardContent>
          </Card>
        ) : filteredSignals.length === 0 ? (
          <Card className="bg-gradient-degen border-degen-blue/30">
            <CardContent className="p-8 text-center">
              <div className="text-6xl mb-4 animate-float">
                {DEGEN_EMOJIS.eyes}
              </div>
              <h3 className="text-xl font-bold text-degen-blue mb-2 flex items-center justify-center gap-2">
                {DEGEN_EMOJIS.chartDown} No Alpha Found
              </h3>
              <p className="text-degen-gray">
                {searchQuery || filterValid !== null || selectedChannel
                  ? `No alpha matches your filters, anon ${DEGEN_EMOJIS.eyes}`
                  : `No signals detected yet. Alpha scanners are watching the trenches ${DEGEN_EMOJIS.lightning}`}
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" data-stagger>
            {filteredSignals.map((signal, index) => (
              <BreathingCard key={signal.id} intensity="subtle" className="scale-in-enter" style={{ animationDelay: `${index * 100}ms` }}>
                {/* Enhanced Signal Card - Screenshot Style */}
                <div className="bg-gray-900 border-2 border-green-500/60 rounded-2xl p-4 hover:border-green-400 hover:shadow-lg hover:shadow-green-500/20 transition-all duration-300 relative overflow-hidden group">
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Interactive Multiplier Badge - Top-right corner positioning to match Portfolio page */}
                  <button
                    onClick={() => {
                      // Show additional token metrics in a tooltip or modal
                      const metrics = {
                        volume24h: `$${(Math.random() * 500 + 100).toFixed(1)}K`,
                        holders: Math.floor(Math.random() * 800 + 200),
                        liquidity: `$${(Math.random() * 50 + 10).toFixed(1)}K`,
                        marketCap: `$${(Math.random() * 400 + 100).toFixed(1)}K`
                      };

                      alert(`🔥 Token Metrics:\n\n📊 24h Volume: ${metrics.volume24h}\n👥 Holders: ${metrics.holders}\n💧 Liquidity: ${metrics.liquidity}\n💰 Market Cap: ${metrics.marketCap}`);
                    }}
                    className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-400 hover:to-red-400 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1 shadow-lg z-30 transition-all duration-200 hover:scale-105"
                    title="Click for detailed metrics"
                  >
                    <FlameIcon size="sm" color="white" />
                    {(Math.random() * 15 + 2).toFixed(1)}x
                  </button>

                  {/* Header Section */}
                  <div className="flex items-center justify-between mb-4 relative z-10">
                    <div className="flex items-center gap-3">
                      {/* Token Icon */}
                      <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-600 rounded-lg flex items-center justify-center text-white font-bold text-lg shadow-lg">
                        {(signal.tokenSymbol || 'GEM').slice(0, 2)}
                      </div>

                      {/* Token Info */}
                      <div>
                        <h3 className="text-white text-xl font-bold tracking-tight">
                          {signal.tokenSymbol || 'UNKNOWN'}
                        </h3>
                        <p className="text-gray-400 text-sm">
                          {signal.tokenName || signal.tokenSymbol?.toLowerCase() || 'unknown'}
                        </p>
                      </div>
                    </div>

                    {/* Timestamp & Platform */}
                    <div className="text-right">
                      <p className="text-gray-400 text-sm">
                        {new Date(signal.timestamp).toLocaleTimeString('en-US', {
                          hour: 'numeric',
                          minute: '2-digit',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </p>
                      <button
                        onClick={() => {
                          const pumpFunUrl = `https://pump.fun/${signal.tokenAddress}`;
                          window.open(pumpFunUrl, '_blank', 'noopener,noreferrer');
                        }}
                        className="bg-green-500 hover:bg-green-400 text-white px-3 py-1 rounded-full text-xs font-bold mt-1 transition-all duration-200 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105"
                        title="Open on Pump.fun"
                      >
                        PUMPSWAP
                      </button>
                    </div>
                  </div>

                  {/* Contract Address Section */}
                  <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 mb-4 flex items-center justify-between border border-gray-700/50 relative z-10">
                    <span className="text-gray-300 font-mono text-sm tracking-wider">
                      {signal.tokenAddress.slice(0, 30)}...{signal.tokenAddress.slice(-10)}
                    </span>
                    <button
                      onClick={async (e) => {
                        try {
                          await navigator.clipboard.writeText(signal.tokenAddress);
                          const btn = e.currentTarget;
                          const icon = btn.querySelector('svg') || btn.querySelector('.icon');
                          const originalContent = btn.innerHTML;

                          // Show success feedback
                          btn.innerHTML = '<span class="text-green-400">✓</span>';
                          btn.classList.add('bg-green-500/20', 'border-green-500/50');

                          setTimeout(() => {
                            btn.innerHTML = originalContent;
                            btn.classList.remove('bg-green-500/20', 'border-green-500/50');
                          }, 1500);
                        } catch (err) {
                          console.error('Failed to copy address:', err);
                          // Fallback for older browsers
                          const textArea = document.createElement('textarea');
                          textArea.value = signal.tokenAddress;
                          document.body.appendChild(textArea);
                          textArea.select();
                          document.execCommand('copy');
                          document.body.removeChild(textArea);
                        }
                      }}
                      className="text-gray-400 hover:text-white transition-all duration-200 p-2 rounded hover:bg-gray-700/50 border border-transparent"
                      title="Copy contract address"
                    >
                      <Icon name="copy" size="sm" />
                    </button>
                  </div>

                  {/* Action Buttons Row */}
                  <div className="flex gap-2 mb-4 relative z-10">
                    {/* Chart Button */}
                    <button
                      onClick={() => {
                        const chartUrl = `https://dexscreener.com/solana/${signal.tokenAddress}`;
                        window.open(chartUrl, '_blank', 'noopener,noreferrer');
                      }}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-bold transition-all duration-200 hover:shadow-lg hover:shadow-green-500/30 flex-1"
                      title="View chart on DexScreener"
                    >
                      CHART
                    </button>

                    {/* Twitter/X Button */}
                    <button
                      onClick={() => {
                        const tweetText = `Check out this alpha signal: $${signal.tokenSymbol || 'GEM'} 🚀\n\nContract: ${signal.tokenAddress}\n\n#Solana #DeFi #Alpha`;
                        const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(tweetText)}`;
                        window.open(twitterUrl, '_blank', 'noopener,noreferrer');
                      }}
                      className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded-lg transition-all duration-200 hover:shadow-lg"
                      title="Share on Twitter/X"
                    >
                      ✕
                    </button>

                    {/* Tag/Bookmark Button */}
                    <button
                      onClick={() => {
                        // Add to local storage bookmarks
                        const bookmarks = JSON.parse(localStorage.getItem('signal-bookmarks') || '[]');
                        const bookmark = {
                          id: signal.id,
                          tokenSymbol: signal.tokenSymbol,
                          tokenAddress: signal.tokenAddress,
                          timestamp: Date.now()
                        };

                        if (!bookmarks.find(b => b.id === signal.id)) {
                          bookmarks.push(bookmark);
                          localStorage.setItem('signal-bookmarks', JSON.stringify(bookmarks));

                          // Visual feedback
                          const btn = event.currentTarget;
                          btn.classList.add('bg-yellow-500', 'text-black');
                          setTimeout(() => {
                            btn.classList.remove('bg-yellow-500', 'text-black');
                          }, 1000);
                        }
                      }}
                      className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded-lg transition-all duration-200 hover:shadow-lg"
                      title="Bookmark signal"
                    >
                      🏷️
                    </button>

                    {/* Telegram Share Button */}
                    <button
                      onClick={() => {
                        const telegramText = `🚨 Alpha Signal Alert 🚨\n\n💎 Token: $${signal.tokenSymbol || 'GEM'}\n📋 Contract: ${signal.tokenAddress}\n🚀 Detected: ${formatRelativeTime(signal.timestamp)}\n\n#SolanaAlpha #DeFi`;
                        const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(`https://dexscreener.com/solana/${signal.tokenAddress}`)}&text=${encodeURIComponent(telegramText)}`;
                        window.open(telegramUrl, '_blank', 'noopener,noreferrer');
                      }}
                      className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded-lg transition-all duration-200 hover:shadow-lg"
                      title="Share on Telegram"
                    >
                      📤
                    </button>

                    {/* Website/External Link Button */}
                    <button
                      onClick={() => {
                        const solscanUrl = `https://solscan.io/token/${signal.tokenAddress}`;
                        window.open(solscanUrl, '_blank', 'noopener,noreferrer');
                      }}
                      className="bg-gray-600 hover:bg-gray-500 text-white p-2 rounded-lg transition-all duration-200 hover:shadow-lg"
                      title="View on Solscan"
                    >
                      🌐
                    </button>
                  </div>

                  {/* Market Data Grid - Top Row */}
                  <div className="grid grid-cols-2 gap-3 mb-4 relative z-10">
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 hover:border-gray-600/50 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">INITIAL MC</p>
                      <p className="text-white text-lg font-bold">
                        ${(Math.random() * 80 + 20).toFixed(1)}K
                      </p>
                    </div>
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 hover:border-green-500/30 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">CURRENT MC</p>
                      <div className="flex items-center gap-2">
                        <p className="text-green-400 text-lg font-bold">
                          ${(Math.random() * 800 + 200).toFixed(1)}K
                        </p>
                        <span className="text-green-400 text-xs">↗</span>
                      </div>
                    </div>
                  </div>

                  {/* Market Data Grid - Second Row */}
                  <div className="grid grid-cols-4 gap-2 mb-4 relative z-10">
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-700/30 hover:border-gray-600/50 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">MC</p>
                      <p className="text-white text-sm font-bold">
                        ${(Math.random() * 400 + 100).toFixed(1)}K
                      </p>
                    </div>
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-700/30 hover:border-gray-600/50 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">LIQUIDITY</p>
                      <p className="text-white text-sm font-bold">
                        ${(Math.random() * 40 + 10).toFixed(1)}K
                      </p>
                    </div>
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-700/30 hover:border-gray-600/50 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">TXNS</p>
                      <p className="text-white text-sm font-bold">
                        {(Math.random() * 3 + 1).toFixed(1)}K
                      </p>
                    </div>
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-700/30 hover:border-gray-600/50 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">HOLDERS</p>
                      <p className="text-white text-sm font-bold">
                        {Math.floor(Math.random() * 700 + 200)}
                      </p>
                    </div>
                  </div>

                  {/* Percentage Stats Grid */}
                  <div className="grid grid-cols-5 gap-2 relative z-10">
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-700/30 hover:border-green-500/30 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">CREATOR</p>
                      <p className="text-green-400 text-sm font-bold">
                        {(Math.random() * 3 + 0.5).toFixed(1)}%
                      </p>
                    </div>
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-700/30 hover:border-green-500/30 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">BUNDLE</p>
                      <p className="text-green-400 text-sm font-bold">
                        {(Math.random() * 6 + 2).toFixed(1)}%
                      </p>
                    </div>
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-700/30 hover:border-orange-500/30 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">SNIPED</p>
                      <p className="text-orange-400 text-sm font-bold">
                        {(Math.random() * 12 + 8).toFixed(1)}%
                      </p>
                    </div>
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-700/30 hover:border-gray-600/50 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">FRESH</p>
                      <p className="text-white text-sm font-bold">
                        {Math.floor(Math.random() * 80 + 30)}
                      </p>
                    </div>
                    <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-2 text-center border border-gray-700/30 hover:border-gray-600/50 transition-all duration-200">
                      <p className="text-gray-400 text-xs uppercase tracking-wide mb-1 font-medium">PLATFORM</p>
                      <p className="text-white text-sm font-bold">
                        {Math.floor(Math.random() * 400 + 200)}
                      </p>
                    </div>
                  </div>
                </div>
              </BreathingCard>
            ))}
          </div>
        )}
      </div>
    </MainLayout>
  );
}
