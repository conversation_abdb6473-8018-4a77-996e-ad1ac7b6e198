'use client';

import React from 'react';

import { LiveIndicator, PulseDot, BreathingCard } from '@/components/ui/LiveIndicator';
import { FlameIcon } from '@/components/ui/FlameIcon';
import { useLivePrice } from '@/hooks/usePriceMonitor';
import { formatCurrency, formatPercentage, formatMultiplier, getChangeColor, shortenAddress, formatRelativeTime } from '@/lib/utils';
import { cn } from '@/lib/utils';
import {
  getDegenTerm,
  formatDegenAmount,
  formatDegenPercentage,
  getDegenMultiplier,
  DEGEN_EMOJIS
} from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';

interface TokenCardProps {
  tokenAddress: string;
  tokenSymbol: string;
  tokenName: string;
  price: number;
  priceChange24h: number;
  marketCap: number;
  volume24h: number;
  multiplierFromSignal?: number;
  signalDate?: Date;
  onTrade?: (action: 'buy' | 'sell') => void;
  className?: string;
}

export function TokenCard({
  tokenAddress,
  tokenSymbol,
  tokenName,
  price,
  priceChange24h,
  marketCap,
  volume24h,
  multiplierFromSignal,
  signalDate,
  onTrade,
  className,
}: TokenCardProps) {
  // Use live price if available, fallback to provided price
  const { price: livePrice, lastUpdate } = useLivePrice(tokenAddress);
  const currentPrice = livePrice || price;

  const isPositive = priceChange24h >= 0;
  const changeColor = getChangeColor(priceChange24h);

  return (
    <div className={cn('bg-gray-900 border-2 border-green-500/60 rounded-2xl p-4 hover:border-green-400 hover:shadow-lg hover:shadow-green-500/20 transition-all duration-300 relative group', className)}>
      {/* Multiplier Badge - Top-right corner positioning */}
      {multiplierFromSignal && (
        <button
          onClick={() => {
            const metrics = {
              volume24h: `$${(volume24h / 1000000).toFixed(1)}M`,
              marketCap: `$${(marketCap / 1000000).toFixed(1)}M`,
              signalTime: signalDate ? formatRelativeTime(signalDate) : 'Unknown',
              multiplier: multiplierFromSignal
            };
            alert(`🔥 Token Metrics:\n\n📊 24h Volume: ${metrics.volume24h}\n💰 Market Cap: ${metrics.marketCap}\n⏰ Signal: ${metrics.signalTime}\n🚀 Multiplier: ${metrics.multiplier}X`);
          }}
          className={`absolute top-2 right-2 px-2.5 py-1 rounded-full text-xs font-bold flex items-center gap-1 shadow-lg transition-all duration-200 hover:scale-105 z-40 min-w-[60px] justify-center ${
            multiplierFromSignal >= 3
              ? 'bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-400 hover:to-red-400 text-white animate-pulse hover:animate-none'
              : multiplierFromSignal >= 2
              ? 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-400 hover:to-emerald-400 text-white'
              : 'bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-400 hover:to-gray-500 text-white'
          }`}
          title="Click for detailed metrics"
          style={{ marginTop: '8px', marginRight: '8px' }}
        >
          <FlameIcon size="sm" color="white" className="mr-1" />
          {getDegenMultiplier(multiplierFromSignal)}
        </button>
      )}

      <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-4 border border-gray-700/30 relative z-10">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold text-white">
                {tokenSymbol}
              </h3>
            </div>
            <p className="text-sm text-gray-400 mt-1 font-medium">
              {tokenName}
            </p>
            <p className="text-xs text-gray-500 mt-1 font-mono">
              {shortenAddress(tokenAddress)}
            </p>
          </div>
        </div>

        <div className="space-y-4">
          {/* Price Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="flex items-center space-x-2">
                <p className="text-sm text-gray-400 font-medium uppercase tracking-wide">
                  Price
                </p>
                {lastUpdate && (
                  <PulseDot color="green" size="sm" />
                )}
              </div>
              <LiveIndicator
                value={currentPrice}
                previousValue={price}
                type="price"
                className="text-lg font-semibold text-white"
              >
                {formatCurrency(currentPrice, 'USD', currentPrice < 0.01 ? 6 : 4)}
              </LiveIndicator>
              {lastUpdate && (
                <p className="text-xs text-gray-500">
                  Updated {formatRelativeTime(lastUpdate)}
                </p>
              )}
            </div>

            <div>
              <p className="text-sm text-gray-400 font-medium uppercase tracking-wide">
                24h Change
              </p>
              <LiveIndicator
                value={priceChange24h}
                type="percentage"
                className={cn('text-lg font-semibold', isPositive ? 'text-green-400' : 'text-red-400')}
              >
                {formatDegenPercentage(priceChange24h)}
              </LiveIndicator>
            </div>
          </div>

          {/* Market Data */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-400 flex items-center gap-1">
                {DEGEN_EMOJIS.chart} Market Cap
              </p>
              <LiveIndicator
                value={marketCap}
                type="volume"
                className="text-sm font-medium text-white"
              >
                {formatCurrency(marketCap, 'USD', 0)}
              </LiveIndicator>
            </div>

            <div>
              <p className="text-sm text-gray-400 flex items-center gap-1">
                {DEGEN_EMOJIS.fire} 24h Volume
              </p>
              <LiveIndicator
                value={volume24h}
                type="volume"
                className="text-sm font-medium text-white"
              >
                {formatCurrency(volume24h, 'USD', 0)}
              </LiveIndicator>
            </div>
          </div>

          {/* Alpha Signal Information */}
          {signalDate && (
            <div className="pt-2 border-t border-gray-700/50">
              <div className="mb-2">
                <p className="text-xs text-gray-400 flex items-center gap-1">
                  <PulseDot color="purple" size="sm" />
                  {DEGEN_EMOJIS.lightning} Alpha detected: {signalDate.toLocaleDateString()} at {signalDate.toLocaleTimeString()}
                </p>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          {onTrade && (
            <div className="flex space-x-2 pt-2">
              <button
                className="flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg font-bold transition-all duration-200 hover:shadow-lg hover:shadow-green-500/30 flex items-center justify-center gap-1"
                onClick={() => {
                  console.log('Buy button clicked for:', tokenSymbol);
                  onTrade('buy');
                }}
                title="Buy this token"
              >
                <Icon name="rocket" size="sm" color="white" />
                BUY 🚀
              </button>
              <button
                className="flex-1 bg-gray-600 hover:bg-gray-500 text-white px-3 py-2 rounded-lg font-bold transition-all duration-200 hover:shadow-lg flex items-center justify-center gap-1"
                onClick={() => {
                  console.log('Sell button clicked for:', tokenSymbol);
                  onTrade('sell');
                }}
                title="Sell this token"
              >
                <Icon name="money" size="sm" color="white" />
                SELL 💰
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Skeleton loader for token cards
export function TokenCardSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-4 animate-pulse', className)}>
      <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-4 border border-gray-700/30">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="h-5 bg-gray-700 rounded w-16 mb-2"></div>
            <div className="h-4 bg-gray-700 rounded w-24 mb-1"></div>
            <div className="h-3 bg-gray-700 rounded w-20"></div>
          </div>
          <div className="h-5 bg-gray-700 rounded w-12"></div>
        </div>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="h-3 bg-gray-700 rounded w-8 mb-1"></div>
              <div className="h-5 bg-gray-700 rounded w-16"></div>
            </div>
            <div>
              <div className="h-3 bg-gray-700 rounded w-12 mb-1"></div>
              <div className="h-5 bg-gray-700 rounded w-12"></div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="h-3 bg-gray-700 rounded w-16 mb-1"></div>
              <div className="h-4 bg-gray-700 rounded w-20"></div>
            </div>
            <div>
              <div className="h-3 bg-gray-700 rounded w-14 mb-1"></div>
              <div className="h-4 bg-gray-700 rounded w-18"></div>
            </div>
          </div>

          <div className="flex space-x-2 pt-2">
            <div className="h-8 bg-gray-700 rounded flex-1"></div>
            <div className="h-8 bg-gray-700 rounded flex-1"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
