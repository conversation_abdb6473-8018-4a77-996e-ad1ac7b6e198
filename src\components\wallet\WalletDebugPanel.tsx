'use client';

import React, { useState, useEffect } from 'react';
import { useWallet } from '@/hooks/useWallet';
import { checkWalletReadiness, getAvailableWallets } from '@/lib/wallet/config';
import { Button } from '@/components/ui/Button';

export function WalletDebugPanel() {
  const { connected, connecting, publicKey, error, connect, disconnect } = useWallet();
  const [walletReadiness, setWalletReadiness] = useState<any>({});
  const [availableWallets, setAvailableWallets] = useState<any[]>([]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setWalletReadiness(checkWalletReadiness());
      setAvailableWallets(getAvailableWallets());
    }
  }, []);

  const refreshWalletStatus = () => {
    if (typeof window !== 'undefined') {
      setWalletReadiness(checkWalletReadiness());
      setAvailableWallets(getAvailableWallets());
    }
  };

  return (
    <div className="bg-gray-900 border border-gray-700 rounded-lg p-4 space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">Wallet Debug Panel</h3>
        <Button onClick={refreshWalletStatus} size="sm" variant="outline">
          Refresh
        </Button>
      </div>

      {/* Connection Status */}
      <div className="space-y-2">
        <h4 className="font-medium text-green-400">Connection Status</h4>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="text-gray-300">Connected:</div>
          <div className={connected ? 'text-green-400' : 'text-red-400'}>
            {connected ? 'Yes' : 'No'}
          </div>
          <div className="text-gray-300">Connecting:</div>
          <div className={connecting ? 'text-yellow-400' : 'text-gray-400'}>
            {connecting ? 'Yes' : 'No'}
          </div>
          <div className="text-gray-300">Public Key:</div>
          <div className="text-gray-400 font-mono text-xs">
            {publicKey ? `${publicKey.toString().slice(0, 8)}...` : 'None'}
          </div>
        </div>
      </div>

      {/* Error Status */}
      {error && (
        <div className="space-y-2">
          <h4 className="font-medium text-red-400">Error</h4>
          <div className="text-sm text-red-300 bg-red-900/20 p-2 rounded">
            {error}
          </div>
        </div>
      )}

      {/* Wallet Readiness */}
      <div className="space-y-2">
        <h4 className="font-medium text-blue-400">Wallet Readiness</h4>
        <div className="grid grid-cols-2 gap-2 text-sm">
          {Object.entries(walletReadiness).map(([wallet, ready]) => (
            <React.Fragment key={wallet}>
              <div className="text-gray-300 capitalize">{wallet}:</div>
              <div className={ready ? 'text-green-400' : 'text-red-400'}>
                {ready ? 'Ready' : 'Not Ready'}
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Available Wallets */}
      <div className="space-y-2">
        <h4 className="font-medium text-purple-400">Available Wallets</h4>
        {availableWallets.length > 0 ? (
          <div className="space-y-1">
            {availableWallets.map((wallet) => (
              <div key={wallet.name} className="text-sm text-gray-300">
                • {wallet.name}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-sm text-gray-500">No wallets detected</div>
        )}
      </div>

      {/* Actions */}
      <div className="space-y-2">
        <h4 className="font-medium text-yellow-400">Actions</h4>
        <div className="flex gap-2">
          {!connected ? (
            <Button onClick={connect} disabled={connecting} size="sm">
              {connecting ? 'Connecting...' : 'Connect'}
            </Button>
          ) : (
            <Button onClick={disconnect} size="sm" variant="outline">
              Disconnect
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
