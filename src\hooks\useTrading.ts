'use client';

import { useState, useEffect, useCallback } from 'react';
import { useWallet } from './useWallet';
import { JupiterTrader } from '@/lib/trading/jupiterTrader';
import { AutoTrader } from '@/lib/trading/autoTrader';
import type { TradeParams, TradeResult } from '@/lib/trading/jupiterTrader';
import type { AutoTradeSettings } from '@/lib/trading/autoTrader';

export function useTrading() {
  const { wallet, connected } = useWallet();
  const [trader, setTrader] = useState<JupiterTrader | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize trader when wallet connects
  useEffect(() => {
    if (connected && wallet) {
      const jupiterTrader = new JupiterTrader(wallet);
      setTrader(jupiterTrader);
      
      // Initialize auto trader
      AutoTrader.getInstance().initialize(wallet);
    } else {
      setTrader(null);
    }
  }, [connected, wallet]);

  const executeBuyTrade = useCallback(async (params: TradeParams): Promise<TradeResult> => {
    if (!trader) {
      return { success: false, error: 'Trader not initialized' };
    }

    try {
      setLoading(true);
      setError(null);
      
      const result = await trader.buyToken(params);
      
      if (!result.success) {
        setError(result.error || 'Trade failed');
      }
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [trader]);

  const executeSellTrade = useCallback(async (params: TradeParams & { percentage?: number }): Promise<TradeResult> => {
    if (!trader) {
      return { success: false, error: 'Trader not initialized' };
    }

    try {
      setLoading(true);
      setError(null);
      
      const result = await trader.sellToken(params);
      
      if (!result.success) {
        setError(result.error || 'Trade failed');
      }
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [trader]);

  return {
    trader,
    connected,
    loading,
    error,
    executeBuyTrade,
    executeSellTrade,
  };
}

export function useAutoTrader() {
  const [isRunning, setIsRunning] = useState(false);
  const [settings, setSettings] = useState<AutoTradeSettings | null>(null);
  const [status, setStatus] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const autoTrader = AutoTrader.getInstance();

  // Update status periodically
  useEffect(() => {
    const updateStatus = () => {
      const currentStatus = autoTrader.getStatus();
      setStatus(currentStatus);
      setIsRunning(currentStatus.isRunning);
      setSettings(currentStatus.settings);
    };

    updateStatus();
    const interval = setInterval(updateStatus, 5000);

    return () => clearInterval(interval);
  }, [autoTrader]);

  const start = useCallback(async () => {
    try {
      setError(null);
      await autoTrader.start();
      setIsRunning(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start auto trader';
      setError(errorMessage);
    }
  }, [autoTrader]);

  const stop = useCallback(() => {
    try {
      setError(null);
      autoTrader.stop();
      setIsRunning(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to stop auto trader';
      setError(errorMessage);
    }
  }, [autoTrader]);

  const updateSettings = useCallback((newSettings: Partial<AutoTradeSettings>) => {
    try {
      setError(null);
      autoTrader.updateSettings(newSettings);
      setSettings(autoTrader.getSettings());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update settings';
      setError(errorMessage);
    }
  }, [autoTrader]);

  const executeManualTrade = useCallback(async (signalId: string, amount?: number) => {
    try {
      setError(null);
      return await autoTrader.executeManualTrade(signalId, amount);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to execute manual trade';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [autoTrader]);

  return {
    isRunning,
    settings,
    status,
    error,
    start,
    stop,
    updateSettings,
    executeManualTrade,
  };
}

export function useQuote() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getQuote = useCallback(async (
    inputMint: string,
    outputMint: string,
    amount: number,
    slippageBps?: number
  ) => {
    try {
      setLoading(true);
      setError(null);

      // This would use Jupiter API directly for quotes
      // Implementation would go here
      
      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get quote';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    getQuote,
    loading,
    error,
  };
}
