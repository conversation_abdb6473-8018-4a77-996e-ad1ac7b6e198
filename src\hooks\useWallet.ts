'use client';

import { useWallet as useSolanaWallet, useConnection } from '@solana/wallet-adapter-react';
import { useWalletModal } from '@solana/wallet-adapter-react-ui';
import { LAMPORTS_PER_SOL, PublicKey } from '@solana/web3.js';
import { useCallback, useEffect, useState } from 'react';
import { WalletBalance, TokenBalance } from '@/types';
import { lamportsToSol } from '@/lib/utils';

export function useWallet() {
  const { connection } = useConnection();
  const wallet = useSolanaWallet();
  const { setVisible } = useWalletModal();
  
  const [balance, setBalance] = useState<WalletBalance | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if wallet is ready
  const isWalletReady = useCallback(() => {
    if (!wallet.wallet) return false;

    // Check if wallet adapter has readyState property
    if ('readyState' in wallet.wallet.adapter) {
      return wallet.wallet.adapter.readyState === 'Installed';
    }

    // Fallback check for older adapters
    return wallet.wallet.adapter.connected !== undefined;
  }, [wallet.wallet]);

  // Connect wallet with proper readiness checks
  const connect = useCallback(async () => {
    try {
      setError(null);

      // If no wallet is selected, show wallet selection modal
      if (!wallet.wallet) {
        setVisible(true);
        return;
      }

      // Check if wallet is ready before attempting connection
      if (!isWalletReady()) {
        const walletName = wallet.wallet.adapter.name;
        setError(`${walletName} wallet is not ready. Please ensure it's installed and unlocked.`);
        return;
      }

      // Only connect if not already connected
      if (!wallet.connected && !wallet.connecting) {
        await wallet.connect();
      }
    } catch (err) {
      console.error('Failed to connect wallet:', err);

      // Handle specific wallet errors with user-friendly messages
      if (err instanceof Error) {
        if (err.name === 'WalletNotReadyError') {
          setError('Wallet is not ready. Please ensure your wallet extension is installed and unlocked.');
        } else if (err.name === 'WalletConnectionError') {
          setError('Failed to connect to wallet. Please try again.');
        } else if (err.name === 'WalletAccountError') {
          setError('No wallet account found. Please create or import an account.');
        } else if (err.message.includes('User rejected')) {
          setError('Connection cancelled by user.');
        } else {
          setError(err.message || 'Failed to connect wallet');
        }
      } else {
        setError('Failed to connect wallet');
      }
    }
  }, [wallet, setVisible, isWalletReady]);

  // Disconnect wallet
  const disconnect = useCallback(async () => {
    try {
      await wallet.disconnect();
      setBalance(null);
      setError(null);
    } catch (err) {
      console.error('Failed to disconnect wallet:', err);
    }
  }, [wallet]);

  // Fetch SOL balance with RPC fallback
  const fetchSolBalance = useCallback(async (): Promise<number> => {
    if (!wallet.publicKey || !connection) return 0;

    // Primary RPC attempt
    try {
      console.log('🔍 Fetching SOL balance from primary RPC...');
      const lamports = await connection.getBalance(wallet.publicKey);
      const balance = lamportsToSol(lamports);
      console.log(`✅ SOL balance fetched successfully: ${balance} SOL`);
      return balance;
    } catch (err) {
      console.error('❌ Primary RPC failed for SOL balance:', err);

      // Check if it's a 403 or rate limit error
      if (err instanceof Error) {
        if (err.message.includes('403') || err.message.includes('rate limit') || err.message.includes('Access forbidden')) {
          console.warn('🔄 RPC access forbidden or rate limited, trying fallback endpoints...');

          // Try fallback RPC endpoints
          const fallbackEndpoints = [
            'https://api.mainnet-beta.solana.com',
            'https://solana-api.projectserum.com',
            'https://rpc.ankr.com/solana'
          ];

          for (const endpoint of fallbackEndpoints) {
            try {
              console.log(`🔄 Trying fallback RPC: ${endpoint}`);
              const { Connection } = await import('@solana/web3.js');
              const fallbackConnection = new Connection(endpoint, 'confirmed');
              const lamports = await fallbackConnection.getBalance(wallet.publicKey);
              const balance = lamportsToSol(lamports);
              console.log(`✅ SOL balance fetched from fallback: ${balance} SOL`);
              return balance;
            } catch (fallbackErr) {
              console.warn(`⚠️ Fallback RPC ${endpoint} failed:`, fallbackErr);
              continue;
            }
          }

          console.error('💥 All RPC endpoints failed for SOL balance');
          return 0;
        }
      }

      console.error('💥 Unexpected error fetching SOL balance:', err);
      return 0;
    }
  }, [wallet.publicKey, connection]);

  // Fetch token balances with RPC fallback
  const fetchTokenBalances = useCallback(async (): Promise<TokenBalance[]> => {
    if (!wallet.publicKey || !connection) return [];

    // Primary RPC attempt
    try {
      console.log('🔍 Fetching token balances from primary RPC...');
      const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
        wallet.publicKey,
        { programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') }
      );

      const balances: TokenBalance[] = [];

      for (const account of tokenAccounts.value) {
        const parsedInfo = account.account.data.parsed.info;
        const mint = parsedInfo.mint;
        const amount = parsedInfo.tokenAmount.uiAmount || 0;
        const decimals = parsedInfo.tokenAmount.decimals;

        if (amount > 0) {
          // TODO: Fetch token metadata and price
          balances.push({
            mint,
            symbol: 'UNKNOWN',
            name: 'Unknown Token',
            amount,
            decimals,
            value: 0, // TODO: Calculate value based on price
          });
        }
      }

      console.log(`✅ Token balances fetched successfully: ${balances.length} tokens`);
      return balances;
    } catch (err) {
      console.error('❌ Primary RPC failed for token balances:', err);

      // Check if it's a 403 or rate limit error
      if (err instanceof Error && (err.message.includes('403') || err.message.includes('rate limit') || err.message.includes('Access forbidden'))) {
        console.warn('🔄 RPC access forbidden or rate limited for token balances, trying fallback endpoints...');

        // Try fallback RPC endpoints
        const fallbackEndpoints = [
          'https://api.mainnet-beta.solana.com',
          'https://solana-api.projectserum.com'
        ];

        for (const endpoint of fallbackEndpoints) {
          try {
            console.log(`🔄 Trying fallback RPC for tokens: ${endpoint}`);
            const { Connection } = await import('@solana/web3.js');
            const fallbackConnection = new Connection(endpoint, 'confirmed');
            const tokenAccounts = await fallbackConnection.getParsedTokenAccountsByOwner(
              wallet.publicKey,
              { programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') }
            );

            const balances: TokenBalance[] = [];

            for (const account of tokenAccounts.value) {
              const parsedInfo = account.account.data.parsed.info;
              const mint = parsedInfo.mint;
              const amount = parsedInfo.tokenAmount.uiAmount || 0;
              const decimals = parsedInfo.tokenAmount.decimals;

              if (amount > 0) {
                balances.push({
                  mint,
                  symbol: 'UNKNOWN',
                  name: 'Unknown Token',
                  amount,
                  decimals,
                  value: 0,
                });
              }
            }

            console.log(`✅ Token balances fetched from fallback: ${balances.length} tokens`);
            return balances;
          } catch (fallbackErr) {
            console.warn(`⚠️ Fallback RPC ${endpoint} failed for tokens:`, fallbackErr);
            continue;
          }
        }

        console.error('💥 All RPC endpoints failed for token balances');
        return [];
      }

      console.error('💥 Unexpected error fetching token balances:', err);
      return [];
    }
  }, [wallet.publicKey, connection]);

  // Fetch all balances
  const fetchBalances = useCallback(async () => {
    if (!wallet.publicKey || !connection) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const [solBalance, tokenBalances] = await Promise.all([
        fetchSolBalance(),
        fetchTokenBalances(),
      ]);

      const totalValue = solBalance; // TODO: Add token values
      
      setBalance({
        sol: solBalance,
        tokens: tokenBalances,
        totalValue,
      });
    } catch (err) {
      console.error('Failed to fetch balances:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch balances');
    } finally {
      setLoading(false);
    }
  }, [wallet.publicKey, connection, fetchSolBalance, fetchTokenBalances]);

  // Auto-fetch balances when wallet connects
  useEffect(() => {
    if (wallet.connected && wallet.publicKey) {
      fetchBalances();
    } else {
      setBalance(null);
    }
  }, [wallet.connected, wallet.publicKey, fetchBalances]);

  // Sign transaction
  const signTransaction = useCallback(async (transaction: any) => {
    if (!wallet.signTransaction) {
      throw new Error('Wallet does not support transaction signing');
    }
    
    return await wallet.signTransaction(transaction);
  }, [wallet.signTransaction]);

  // Sign all transactions
  const signAllTransactions = useCallback(async (transactions: any[]) => {
    if (!wallet.signAllTransactions) {
      throw new Error('Wallet does not support signing multiple transactions');
    }
    
    return await wallet.signAllTransactions(transactions);
  }, [wallet.signAllTransactions]);

  // Sign message
  const signMessage = useCallback(async (message: Uint8Array) => {
    if (!wallet.signMessage) {
      throw new Error('Wallet does not support message signing');
    }
    
    return await wallet.signMessage(message);
  }, [wallet.signMessage]);

  return {
    // Wallet state
    publicKey: wallet.publicKey,
    connected: wallet.connected,
    connecting: wallet.connecting,
    disconnecting: wallet.disconnecting,
    wallet: wallet.wallet,
    
    // Balance state
    balance,
    loading,
    error,
    
    // Actions
    connect,
    disconnect,
    fetchBalances,
    signTransaction,
    signAllTransactions,
    signMessage,
    
    // Utils
    shortAddress: wallet.publicKey ? 
      `${wallet.publicKey.toString().slice(0, 4)}...${wallet.publicKey.toString().slice(-4)}` : 
      null,
  };
}
