// Signal Service - Handles Telegram signal and channel operations

import { supabase } from '../client';
import type { TelegramSignal, TelegramChannel, ParsedSignal } from '@/types';

export class SignalService {
  /**
   * Get all active Telegram channels
   */
  static async getActiveChannels(): Promise<TelegramChannel[]> {
    try {
      // Check if we're using mock/demo Supabase configuration
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      if (supabaseUrl?.includes('mock-project-id') || supabaseUrl?.includes('demo')) {
        console.warn('⚠️ Using mock Supabase configuration - returning demo active channels');
        return this.getDemoChannels().filter(channel => channel.active);
      }

      const { data, error } = await supabase
        .from('telegram_channels')
        .select('*')
        .eq('active', true)
        .order('signal_count', { ascending: false });

      if (error) {
        console.error('Error fetching active channels:', error);
        // Return demo active channels on error for development
        return this.getDemoChannels().filter(channel => channel.active);
      }

      return data.map(this.mapChannel);
    } catch (error) {
      console.error('Error in getActiveChannels:', error);
      // Return demo active channels on exception for development
      return this.getDemoChannels().filter(channel => channel.active);
    }
  }

  /**
   * Get all Telegram channels (active and inactive)
   */
  static async getAllChannels(): Promise<TelegramChannel[]> {
    try {
      console.log('🔍 Attempting to fetch all channels from Supabase...');

      // Check if we're using mock/demo Supabase configuration
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      if (supabaseUrl?.includes('mock-project-id') || supabaseUrl?.includes('demo')) {
        console.warn('⚠️ Using mock Supabase configuration - returning demo channels');
        return this.getDemoChannels();
      }

      // First, test the connection
      const { data: testData, error: testError } = await supabase
        .from('telegram_channels')
        .select('count(*)', { count: 'exact' });

      console.log('📊 Connection test result:', { testData, testError });

      const { data, error } = await supabase
        .from('telegram_channels')
        .select('*')
        .order('created_at', { ascending: false });

      console.log('📋 Raw Supabase response:', { data, error, dataType: typeof data, isArray: Array.isArray(data) });

      if (error) {
        console.error('❌ Error fetching all channels:', error);
        console.error('Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });

        // If it's a connection error, return demo channels for development
        if (error.message?.includes('Failed to fetch') || error.message?.includes('network')) {
          console.warn('🔄 Network error detected - returning demo channels for development');
          return this.getDemoChannels();
        }

        return [];
      }

      if (!data) {
        console.warn('⚠️ No data returned from Supabase');
        return [];
      }

      if (!Array.isArray(data)) {
        console.error('❌ Data is not an array:', data);
        return [];
      }

      console.log(`✅ Successfully fetched ${data.length} channels`);
      const mappedChannels = data.map(this.mapChannel);
      console.log('🔄 Mapped channels:', mappedChannels);

      return mappedChannels;
    } catch (error) {
      console.error('💥 Exception in getAllChannels:', error);
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');

      // Return demo channels for development when there's an exception
      console.warn('🔄 Exception caught - returning demo channels for development');
      return this.getDemoChannels();
    }
  }

  /**
   * Get recent signals
   */
  static async getRecentSignals(limit: number = 50): Promise<TelegramSignal[]> {
    try {
      console.log('🔍 Attempting to fetch recent signals from Supabase...');

      // Check if we're using mock/demo Supabase configuration
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      if (supabaseUrl?.includes('mock-project-id') || supabaseUrl?.includes('demo')) {
        console.warn('⚠️ Using mock Supabase configuration - returning demo signals');
        return this.getDemoSignals(limit);
      }

      const { data, error } = await supabase
        .from('telegram_signals')
        .select('*')
        .eq('valid', true)
        .order('timestamp', { ascending: false })
        .limit(limit);

      console.log('📋 Raw signals response:', { data, error, dataType: typeof data, isArray: Array.isArray(data) });

      if (error) {
        console.error('❌ Error fetching recent signals:', error);
        console.error('Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });

        // Return demo signals on error for development
        console.warn('🔄 Error detected - returning demo signals for development');
        return this.getDemoSignals(limit);
      }

      if (!data) {
        console.warn('⚠️ No signals data returned from Supabase');
        return this.getDemoSignals(limit);
      }

      if (!Array.isArray(data)) {
        console.error('❌ Signals data is not an array:', data);
        return this.getDemoSignals(limit);
      }

      console.log(`✅ Successfully fetched ${data.length} signals`);
      const mappedSignals = data.map(this.mapSignal);
      console.log('🔄 Mapped signals:', mappedSignals);

      return mappedSignals;
    } catch (error) {
      console.error('💥 Exception in getRecentSignals:', error);
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');

      // Return demo signals for development when there's an exception
      console.warn('🔄 Exception caught - returning demo signals for development');
      return this.getDemoSignals(limit);
    }
  }

  /**
   * Get signals by token address
   */
  static async getSignalsByToken(tokenAddress: string): Promise<TelegramSignal[]> {
    try {
      const { data, error } = await supabase
        .from('telegram_signals')
        .select('*')
        .eq('token_address', tokenAddress)
        .eq('valid', true)
        .order('timestamp', { ascending: false });

      if (error) {
        console.error('Error fetching signals by token:', error);
        return [];
      }

      return data.map(this.mapSignal);
    } catch (error) {
      console.error('Error in getSignalsByToken:', error);
      return [];
    }
  }

  /**
   * Add a new Telegram channel
   */
  static async addChannel(channel: Omit<TelegramChannel, 'id' | 'createdAt' | 'updatedAt' | 'lastSignal' | 'signalCount'>): Promise<TelegramChannel | null> {
    try {
      // Input validation
      if (!channel || typeof channel !== 'object') {
        console.error('[VALIDATION] Invalid channel object provided:', channel);
        return null;
      }

      if (!channel.name || !channel.username) {
        console.error('[VALIDATION] Channel name and username are required:', { name: channel.name, username: channel.username });
        return null;
      }

      console.log(`[ADD] Starting channel addition: ${channel.name} (@${channel.username})`);

      // Check if we're using mock/demo Supabase configuration
      if (this.isMockEnvironment()) {
        console.log(`[MOCK] Adding channel: ${channel.name} (@${channel.username})`);
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Return mock channel with generated ID
        const mockChannel: TelegramChannel = {
          id: `mock_${Date.now()}`,
          name: channel.name,
          username: channel.username,
          description: channel.description || '',
          memberCount: channel.memberCount || 0,
          active: channel.active,
          signalCount: 0,
          lastSignal: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        console.log(`[MOCK] Successfully created channel:`, mockChannel);
        return mockChannel;
      }

      const { data, error } = await supabase
        .from('telegram_channels')
        .insert({
          name: channel.name,
          username: channel.username,
          description: channel.description,
          member_count: channel.memberCount,
          active: channel.active,
        })
        .select()
        .single();

      if (error) {
        console.error('[SUPABASE] Error adding channel:', {
          error,
          channel,
          errorMessage: error.message,
          errorDetails: error.details,
          errorHint: error.hint,
          errorCode: error.code
        });
        return null;
      }

      const mappedChannel = this.mapChannel(data);
      console.log(`[SUCCESS] Channel added successfully:`, mappedChannel);
      return mappedChannel;
    } catch (error) {
      console.error('[EXCEPTION] Error in addChannel:', {
        error,
        channel,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorStack: error instanceof Error ? error.stack : 'No stack trace'
      });
      return null;
    }
  }

  /**
   * Check if we're using mock/demo configuration
   */
  private static isMockEnvironment(): boolean {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const isMock = supabaseUrl?.includes('mock-project-id') ||
                   supabaseUrl?.includes('demo') ||
                   supabaseUrl?.includes('localhost') ||
                   !supabaseUrl ||
                   supabaseUrl === '';

    console.log(`[ENV] Supabase URL: ${supabaseUrl}, Is Mock: ${isMock}`);
    return isMock;
  }

  /**
   * Update channel status
   */
  static async updateChannelStatus(channelId: string, active: boolean): Promise<boolean> {
    try {
      // Input validation
      if (!channelId || typeof channelId !== 'string') {
        console.error('[VALIDATION] Invalid channelId provided:', channelId);
        return false;
      }

      if (typeof active !== 'boolean') {
        console.error('[VALIDATION] Invalid active status provided:', active);
        return false;
      }

      console.log(`[UPDATE] Starting channel status update: ${channelId} -> ${active}`);

      // Check if we're using mock/demo Supabase configuration
      if (this.isMockEnvironment()) {
        console.log(`[MOCK] Updating channel ${channelId} status to ${active ? 'active' : 'inactive'}`);
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log(`[MOCK] Successfully updated channel ${channelId}`);
        return true;
      }

      console.log(`[PRODUCTION] Attempting to update channel ${channelId} status to ${active}`);

      const { error } = await supabase
        .from('telegram_channels')
        .update({ active })
        .eq('id', channelId);

      if (error) {
        console.error('[SUPABASE] Error updating channel status:', {
          error,
          channelId,
          active,
          errorMessage: error.message,
          errorDetails: error.details,
          errorHint: error.hint,
          errorCode: error.code
        });
        return false;
      }

      console.log(`[SUCCESS] Channel ${channelId} status updated to ${active}`);
      return true;
    } catch (error) {
      console.error('[EXCEPTION] Error in updateChannelStatus:', {
        error,
        channelId,
        active,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorStack: error instanceof Error ? error.stack : 'No stack trace'
      });
      return false;
    }
  }

  /**
   * Update channel information
   */
  static async updateChannel(channelId: string, updates: Partial<Omit<TelegramChannel, 'id' | 'createdAt' | 'updatedAt'>>): Promise<TelegramChannel | null> {
    try {
      const updateData: any = {};

      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.username !== undefined) updateData.username = updates.username;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.memberCount !== undefined) updateData.member_count = updates.memberCount;
      if (updates.active !== undefined) updateData.active = updates.active;

      const { data, error } = await supabase
        .from('telegram_channels')
        .update(updateData)
        .eq('id', channelId)
        .select()
        .single();

      if (error) {
        console.error('Error updating channel:', error);
        return null;
      }

      return this.mapChannel(data);
    } catch (error) {
      console.error('Error in updateChannel:', error);
      return null;
    }
  }

  /**
   * Delete a Telegram channel
   */
  static async deleteChannel(channelId: string): Promise<boolean> {
    try {
      // Input validation
      if (!channelId || typeof channelId !== 'string') {
        console.error('[VALIDATION] Invalid channelId provided for deletion:', channelId);
        return false;
      }

      console.log(`[DELETE] Starting channel deletion: ${channelId}`);

      // Check if we're using mock/demo Supabase configuration
      if (this.isMockEnvironment()) {
        console.log(`[MOCK] Deleting channel ${channelId}`);
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log(`[MOCK] Successfully deleted channel ${channelId}`);
        return true;
      }

      const { error } = await supabase
        .from('telegram_channels')
        .delete()
        .eq('id', channelId);

      if (error) {
        console.error('[SUPABASE] Error deleting channel:', {
          error,
          channelId,
          errorMessage: error.message,
          errorDetails: error.details,
          errorHint: error.hint,
          errorCode: error.code
        });
        return false;
      }

      console.log(`[SUCCESS] Channel ${channelId} deleted successfully`);
      return true;
    } catch (error) {
      console.error('[EXCEPTION] Error in deleteChannel:', {
        error,
        channelId,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorStack: error instanceof Error ? error.stack : 'No stack trace'
      });
      return false;
    }
  }

  /**
   * Record a new signal
   */
  static async recordSignal(signal: Omit<TelegramSignal, 'id' | 'createdAt'>): Promise<TelegramSignal | null> {
    try {
      const { data, error } = await supabase
        .from('telegram_signals')
        .insert({
          channel_id: signal.channelId,
          channel_name: signal.channelName,
          message_id: signal.messageId,
          content: signal.content,
          token_address: signal.tokenAddress,
          token_symbol: signal.tokenSymbol,
          market_cap: signal.marketCap,
          images: signal.images,
          timestamp: signal.timestamp.toISOString(),
          processed: signal.processed,
          valid: signal.valid,
          error: signal.error,
        })
        .select()
        .single();

      if (error) {
        console.error('Error recording signal:', error);
        return null;
      }

      // Update channel signal count and last signal time
      await this.updateChannelSignalStats(signal.channelId, signal.timestamp);

      return this.mapSignal(data);
    } catch (error) {
      console.error('Error in recordSignal:', error);
      return null;
    }
  }

  /**
   * Update signal processing status
   */
  static async updateSignalStatus(
    signalId: string, 
    processed: boolean, 
    valid: boolean, 
    error?: string
  ): Promise<boolean> {
    try {
      const { error: updateError } = await supabase
        .from('telegram_signals')
        .update({
          processed,
          valid,
          error,
        })
        .eq('id', signalId);

      if (updateError) {
        console.error('Error updating signal status:', updateError);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateSignalStatus:', error);
      return false;
    }
  }

  /**
   * Get unprocessed signals
   */
  static async getUnprocessedSignals(): Promise<TelegramSignal[]> {
    try {
      const { data, error } = await supabase
        .from('telegram_signals')
        .select('*')
        .eq('processed', false)
        .order('timestamp', { ascending: true });

      if (error) {
        console.error('Error fetching unprocessed signals:', error);
        return [];
      }

      return data.map(this.mapSignal);
    } catch (error) {
      console.error('Error in getUnprocessedSignals:', error);
      return [];
    }
  }

  /**
   * Search signals by content
   */
  static async searchSignals(query: string, limit: number = 20): Promise<TelegramSignal[]> {
    try {
      const { data, error } = await supabase
        .from('telegram_signals')
        .select('*')
        .textSearch('content', query)
        .eq('valid', true)
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error searching signals:', error);
        return [];
      }

      return data.map(this.mapSignal);
    } catch (error) {
      console.error('Error in searchSignals:', error);
      return [];
    }
  }

  /**
   * Subscribe to new signals
   */
  static subscribeToNewSignals(callback: (signal: TelegramSignal) => void) {
    return supabase
      .channel('new-signals')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'telegram_signals',
          filter: 'valid=eq.true',
        },
        (payload) => {
          const signal = this.mapSignal(payload.new as any);
          callback(signal);
        }
      )
      .subscribe();
  }

  /**
   * Subscribe to signal updates
   */
  static subscribeToSignalUpdates(callback: (signal: TelegramSignal) => void) {
    return supabase
      .channel('signal-updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'telegram_signals',
        },
        (payload) => {
          const signal = this.mapSignal(payload.new as any);
          callback(signal);
        }
      )
      .subscribe();
  }

  /**
   * Update channel signal statistics
   */
  private static async updateChannelSignalStats(channelId: string, lastSignalTime: Date): Promise<void> {
    try {
      const { error } = await supabase.rpc('increment_channel_signal_count', {
        channel_id: channelId,
        last_signal_time: lastSignalTime.toISOString(),
      });

      if (error) {
        console.error('Error updating channel stats:', error);
      }
    } catch (error) {
      console.error('Error in updateChannelSignalStats:', error);
    }
  }

  /**
   * Get demo channels for development when Supabase is not configured
   */
  private static getDemoChannels(): TelegramChannel[] {
    return [
      {
        id: 'demo-1',
        name: 'Crypto Signals Pro',
        username: 'cryptosignalspro',
        description: 'Demo channel for development - Premium crypto trading signals',
        memberCount: 15420,
        active: true,
        lastSignal: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        signalCount: 127,
      },
      {
        id: 'demo-2',
        name: 'Alpha Hunters',
        username: 'alphahunters',
        description: 'Demo channel for development - Early alpha calls and gems',
        memberCount: 8930,
        active: true,
        lastSignal: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        signalCount: 89,
      },
      {
        id: 'demo-3',
        name: 'Degen Calls',
        username: 'degencalls',
        description: 'Demo channel for development - High-risk high-reward plays',
        memberCount: 23100,
        active: false,
        lastSignal: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        signalCount: 203,
      },
    ];
  }

  /**
   * Get demo signals for development when Supabase is not configured
   */
  private static getDemoSignals(limit: number = 50): TelegramSignal[] {
    const demoSignals: TelegramSignal[] = [
      {
        id: 'demo-signal-1',
        channelId: 'demo-1',
        channelName: 'Crypto Signals Pro',
        messageId: 12345,
        content: '🚀 $BONK - New Solana memecoin with massive potential!\n\nCA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263\n\n📊 Market Cap: $2.5M\n💧 Liquidity: $450K\n🔥 24h Volume: $1.2M\n\n⚡ ENTRY: $0.000012 - $0.000015\n🎯 TARGET 1: $0.000025 (100%)\n🎯 TARGET 2: $0.000045 (250%)\n🛑 STOP LOSS: $0.000008 (-33%)\n\n#BONK #Solana #Memecoin',
        tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
        tokenSymbol: 'BONK',
        timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
        valid: true,
        processed: true,
        confidence: 0.92,
        sentiment: 'bullish',
        targets: [0.000025, 0.000045],
        stopLoss: 0.000008,
        entryPrice: 0.000013,
        multiplier: 2.5,
      },
      {
        id: 'demo-signal-2',
        channelId: 'demo-2',
        channelName: 'Alpha Hunters',
        messageId: 12346,
        content: '💎 $PEPE - Massive breakout incoming!\n\nCA: 6GCACwEBRE3yE24d3gEz7b5S8vwxmQKbvxEH1Lbpump\n\n📈 Technical Analysis:\n- Breaking above key resistance\n- Volume spike detected\n- RSI showing bullish divergence\n\n⚡ ENTRY: $0.00001850 - $0.00001950\n🎯 TARGET: $0.00002800 (50%)\n🛑 STOP: $0.00001650 (-10%)\n\n#PEPE #Breakout #TechnicalAnalysis',
        tokenAddress: '6GCACwEBRE3yE24d3gEz7b5S8vwxmQKbvxEH1Lbpump',
        tokenSymbol: 'PEPE',
        timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
        valid: true,
        processed: true,
        confidence: 0.87,
        sentiment: 'bullish',
        targets: [0.00002800],
        stopLoss: 0.00001650,
        entryPrice: 0.00001900,
        multiplier: 1.5,
      },
      {
        id: 'demo-signal-3',
        channelId: 'demo-1',
        channelName: 'Crypto Signals Pro',
        messageId: 12347,
        content: '🔥 $WIF - Dogwifhat showing strong momentum!\n\nCA: EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\n\n📊 Metrics:\n- Market Cap: $1.8B\n- 24h Volume: $125M\n- Holders: 89,432\n\n⚡ ENTRY: $2.45 - $2.65\n🎯 TARGET 1: $3.20 (25%)\n🎯 TARGET 2: $4.10 (60%)\n🛑 STOP LOSS: $2.15 (-15%)\n\n#WIF #Solana #DogCoin',
        tokenAddress: 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm',
        tokenSymbol: 'WIF',
        timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago
        valid: true,
        processed: true,
        confidence: 0.89,
        sentiment: 'bullish',
        targets: [3.20, 4.10],
        stopLoss: 2.15,
        entryPrice: 2.55,
        multiplier: 1.6,
      },
      {
        id: 'demo-signal-4',
        channelId: 'demo-2',
        channelName: 'Alpha Hunters',
        messageId: 12348,
        content: '⚠️ $DOGE - Caution advised, bearish signals detected\n\nCA: 4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R\n\n📉 Analysis:\n- Breaking below support\n- Decreasing volume\n- Whale selling detected\n\n⚡ SHORT ENTRY: $0.085 - $0.090\n🎯 TARGET: $0.065 (-25%)\n🛑 STOP: $0.095 (+8%)\n\n#DOGE #Bearish #ShortSetup',
        tokenAddress: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
        tokenSymbol: 'DOGE',
        timestamp: new Date(Date.now() - 1000 * 60 * 120), // 2 hours ago
        valid: true,
        processed: true,
        confidence: 0.78,
        sentiment: 'bearish',
        targets: [0.065],
        stopLoss: 0.095,
        entryPrice: 0.087,
        multiplier: 0.75,
      },
      {
        id: 'demo-signal-5',
        channelId: 'demo-3',
        channelName: 'Degen Calls',
        messageId: 12349,
        content: '🌙 $MOON - Moonshot potential detected!\n\nCA: So11111111111111111111111111111111111111112\n\n🚀 Ultra High Risk/Reward:\n- New launch, low market cap\n- Strong community building\n- Viral potential on social media\n\n⚡ ENTRY: $0.0000001 - $0.0000002\n🎯 TARGET: $0.000001 (500%+)\n🛑 STOP: $0.00000005 (-50%)\n\n⚠️ EXTREME RISK - Only invest what you can lose!\n\n#MOON #Moonshot #HighRisk',
        tokenAddress: 'So11111111111111111111111111111111111111112',
        tokenSymbol: 'MOON',
        timestamp: new Date(Date.now() - 1000 * 60 * 180), // 3 hours ago
        valid: true,
        processed: false,
        confidence: 0.65,
        sentiment: 'bullish',
        targets: [0.000001],
        stopLoss: 0.00000005,
        entryPrice: 0.00000015,
        multiplier: 6.7,
      },
    ];

    return demoSignals.slice(0, limit);
  }

  /**
   * Map database channel to TelegramChannel type
   */
  private static mapChannel(data: any): TelegramChannel {
    console.log('🔄 Mapping channel data:', data);

    try {
      const mapped = {
        id: data.id,
        name: data.name,
        username: data.username,
        description: data.description,
        memberCount: data.member_count || 0,
        active: data.active || false,
        lastSignal: data.last_signal ? new Date(data.last_signal) : undefined,
        signalCount: data.signal_count || 0,
      };

      console.log('✅ Successfully mapped channel:', mapped);
      return mapped;
    } catch (error) {
      console.error('❌ Error mapping channel:', error, 'Data:', data);
      throw error;
    }
  }

  /**
   * Map database signal to TelegramSignal type
   */
  private static mapSignal(data: any): TelegramSignal {
    return {
      id: data.id,
      channelId: data.channel_id,
      channelName: data.channel_name,
      messageId: data.message_id,
      content: data.content,
      tokenAddress: data.token_address,
      tokenSymbol: data.token_symbol,
      marketCap: data.market_cap ? Number(data.market_cap) : undefined,
      images: data.images,
      timestamp: new Date(data.timestamp),
      processed: data.processed,
      valid: data.valid,
      error: data.error,
    };
  }
}
