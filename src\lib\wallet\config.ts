// Solana Wallet Configuration

import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { clusterApiUrl } from '@solana/web3.js';
import { SOLANA_CONFIG } from '../constants';

// Get network from environment
export const network = (SOLANA_CONFIG.network as WalletAdapterNetwork) || WalletAdapterNetwork.Mainnet;

// Get RPC endpoint
export const endpoint = SOLANA_CONFIG.rpcUrl || clusterApiUrl(network);

// HMR-safe wallet adapter cache with enhanced stability
let walletAdapterCache: any[] | null = null;
let lastCacheTime = 0;
const CACHE_DURATION = 5000; // 5 seconds cache to handle HMR updates
let isInitializing = false; // Prevent concurrent initialization

// Enhanced module factory validation helper with Turbopack compatibility
const validateWalletModule = (module: any, adapterName: string) => {
  if (!module || typeof module !== 'object') {
    console.warn(`⚠️ ${adapterName} module is invalid or null`);
    return false;
  }

  const AdapterConstructor = module[adapterName];
  if (!AdapterConstructor) {
    console.warn(`⚠️ ${adapterName} constructor not found in module`);
    return false;
  }

  if (typeof AdapterConstructor !== 'function') {
    console.warn(`⚠️ ${adapterName} constructor is not a function`);
    return false;
  }

  // Additional validation for constructor prototype
  if (!AdapterConstructor.prototype) {
    console.warn(`⚠️ ${adapterName} constructor missing prototype`);
    return false;
  }

  // Turbopack-specific validation: check for module factory availability
  try {
    // Test if we can create a temporary instance to validate the constructor
    const testInstance = new AdapterConstructor();
    if (!testInstance || typeof testInstance !== 'object') {
      console.warn(`⚠️ ${adapterName} constructor test instantiation failed`);
      return false;
    }
  } catch (error) {
    // If instantiation fails due to missing dependencies, that's expected
    // But if it fails due to module factory issues, we need to catch that
    if (error instanceof Error && (
      error.message.includes('module factory') ||
      error.message.includes('Cannot access before initialization') ||
      error.message.includes('is not a constructor')
    )) {
      console.warn(`⚠️ ${adapterName} module factory validation failed:`, error.message);
      return false;
    }
    // Other errors during instantiation are acceptable (e.g., missing wallet extension)
  }

  return true;
};

// Utility function to detect and handle chunk loading errors
const isChunkLoadingError = (error: Error): boolean => {
  return error.message.includes('Loading chunk') ||
         error.message.includes('Failed to load chunk') ||
         error.message.includes('ChunkLoadError') ||
         error.name === 'ChunkLoadError' ||
         error.message.includes('Loading CSS chunk') ||
         error.message.includes('Loading JS chunk');
};

// Enhanced retry mechanism for chunk loading errors
const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  operationName: string = 'operation'
): Promise<T | null> => {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (error instanceof Error && isChunkLoadingError(error)) {
        if (attempt < maxRetries - 1) {
          const delay = baseDelay * Math.pow(2, attempt); // Exponential backoff
          console.warn(`⚠️ ${operationName} chunk loading failed (attempt ${attempt + 1}/${maxRetries}), retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        } else {
          console.error(`❌ ${operationName} chunk loading failed after ${maxRetries} attempts:`, error.message);
          return null;
        }
      } else {
        // Non-chunk errors should be handled by the caller
        throw error;
      }
    }
  }
  return null;
};

// HMR-safe wallet adapter loaders with enhanced validation and module factory detection
const loadPhantomWallet = async () => {
  try {
    // Enhanced dynamic import with module factory validation
    const module = await import('@solana/wallet-adapter-phantom');

    // Validate module structure and factory availability
    if (!validateWalletModule(module, 'PhantomWalletAdapter')) {
      console.warn('⚠️ PhantomWalletAdapter module validation failed');
      return null;
    }

    const { PhantomWalletAdapter } = module;

    // Create instance with enhanced error handling
    let instance;
    try {
      instance = new PhantomWalletAdapter();
    } catch (constructorError) {
      if (constructorError instanceof Error && (
        constructorError.message.includes('module factory') ||
        constructorError.message.includes('Cannot access before initialization')
      )) {
        console.warn('⚠️ PhantomWalletAdapter constructor failed due to HMR module factory issue:', constructorError.message);
        return null;
      }
      // Re-throw other constructor errors as they might be expected
      throw constructorError;
    }

    // Validate instance creation
    if (!instance || typeof instance.connect !== 'function') {
      console.warn('⚠️ PhantomWalletAdapter instance validation failed');
      return null;
    }

    console.log('✅ Successfully loaded PhantomWalletAdapter');
    return instance;
  } catch (error) {
    if (error instanceof Error && (
      error.message.includes('module factory') ||
      error.message.includes('HMR') ||
      error.message.includes('chunk')
    )) {
      console.warn('⚠️ PhantomWalletAdapter failed due to HMR/module factory issue:', error.message);
    } else {
      console.warn('⚠️ Failed to load PhantomWalletAdapter:', error);
    }
    return null;
  }
};

const loadSolflareWallet = async () => {
  const loadOperation = async () => {
    // Enhanced dynamic import with chunk loading error handling
    const module = await import('@solana/wallet-adapter-solflare');

    // Validate module structure and factory availability
    if (!validateWalletModule(module, 'SolflareWalletAdapter')) {
      console.warn('⚠️ SolflareWalletAdapter module validation failed');
      return null;
    }

    const { SolflareWalletAdapter } = module;

    // Create instance with enhanced error handling
    let instance;
    try {
      instance = new SolflareWalletAdapter({ network });
    } catch (constructorError) {
      if (constructorError instanceof Error && (
        constructorError.message.includes('module factory') ||
        constructorError.message.includes('Cannot access before initialization')
      )) {
        console.warn('⚠️ SolflareWalletAdapter constructor failed due to HMR module factory issue:', constructorError.message);
        return null;
      }
      // Re-throw other constructor errors as they might be expected
      throw constructorError;
    }

    // Validate instance creation
    if (!instance || typeof instance.connect !== 'function') {
      console.warn('⚠️ SolflareWalletAdapter instance validation failed');
      return null;
    }

    console.log('✅ Successfully loaded SolflareWalletAdapter');
    return instance;
  };

  try {
    // Use the enhanced retry mechanism for chunk loading errors
    const result = await retryWithBackoff(loadOperation, 3, 1000, 'SolflareWalletAdapter');
    return result;
  } catch (error) {
    if (error instanceof Error) {
      const isModuleError = error.message.includes('module factory') ||
                          error.message.includes('HMR') ||
                          error.message.includes('Cannot access before initialization');

      if (isModuleError) {
        console.warn('⚠️ SolflareWalletAdapter failed due to HMR/module factory issue:', error.message);
      } else {
        console.warn('⚠️ Failed to load SolflareWalletAdapter:', error);
      }
    }
    return null;
  }
};

const loadBackpackWallet = async () => {
  try {
    // Enhanced dynamic import with module factory validation
    const module = await import('@solana/wallet-adapter-backpack');

    // Validate module structure and factory availability
    if (!validateWalletModule(module, 'BackpackWalletAdapter')) {
      console.warn('⚠️ BackpackWalletAdapter module validation failed');
      return null;
    }

    const { BackpackWalletAdapter } = module;

    // Create instance with enhanced error handling
    let instance;
    try {
      instance = new BackpackWalletAdapter();
    } catch (constructorError) {
      if (constructorError instanceof Error && (
        constructorError.message.includes('module factory') ||
        constructorError.message.includes('Cannot access before initialization')
      )) {
        console.warn('⚠️ BackpackWalletAdapter constructor failed due to HMR module factory issue:', constructorError.message);
        return null;
      }
      // Re-throw other constructor errors as they might be expected
      throw constructorError;
    }

    // Validate instance creation
    if (!instance || typeof instance.connect !== 'function') {
      console.warn('⚠️ BackpackWalletAdapter instance validation failed');
      return null;
    }

    console.log('✅ Successfully loaded BackpackWalletAdapter');
    return instance;
  } catch (error) {
    if (error instanceof Error && (
      error.message.includes('module factory') ||
      error.message.includes('HMR') ||
      error.message.includes('chunk')
    )) {
      console.warn('⚠️ BackpackWalletAdapter failed due to HMR/module factory issue:', error.message);
    } else {
      console.warn('⚠️ Failed to load BackpackWalletAdapter:', error);
    }
    return null;
  }
};

const loadGlowWallet = async () => {
  try {
    // Enhanced dynamic import with module factory validation
    const module = await import('@solana/wallet-adapter-glow');

    // Validate module structure and factory availability
    if (!validateWalletModule(module, 'GlowWalletAdapter')) {
      console.warn('⚠️ GlowWalletAdapter module validation failed');
      return null;
    }

    const { GlowWalletAdapter } = module;

    // Create instance with enhanced error handling
    let instance;
    try {
      instance = new GlowWalletAdapter();
    } catch (constructorError) {
      if (constructorError instanceof Error && (
        constructorError.message.includes('module factory') ||
        constructorError.message.includes('Cannot access before initialization')
      )) {
        console.warn('⚠️ GlowWalletAdapter constructor failed due to HMR module factory issue:', constructorError.message);
        return null;
      }
      // Re-throw other constructor errors as they might be expected
      throw constructorError;
    }

    // Validate instance creation
    if (!instance || typeof instance.connect !== 'function') {
      console.warn('⚠️ GlowWalletAdapter instance validation failed');
      return null;
    }

    console.log('✅ Successfully loaded GlowWalletAdapter');
    return instance;
  } catch (error) {
    if (error instanceof Error && (
      error.message.includes('module factory') ||
      error.message.includes('HMR') ||
      error.message.includes('chunk')
    )) {
      console.warn('⚠️ GlowWalletAdapter failed due to HMR/module factory issue:', error.message);
    } else {
      console.warn('⚠️ Failed to load GlowWalletAdapter:', error);
    }
    return null;
  }
};

// HMR-resilient wallet initialization with caching and validation
export const getWallets = async () => {
  // Only run on client-side
  if (typeof window === 'undefined') {
    return [];
  }

  // Return cached wallets if still valid (helps with HMR stability)
  const now = Date.now();
  if (walletAdapterCache && (now - lastCacheTime) < CACHE_DURATION) {
    console.log(`🔄 Using cached wallet adapters (${walletAdapterCache.length} wallets)`);
    return walletAdapterCache;
  }

  // Prevent concurrent initialization during HMR updates
  if (isInitializing) {
    console.log('⏳ Wallet initialization already in progress, waiting...');
    // Wait for current initialization to complete
    let attempts = 0;
    while (isInitializing && attempts < 50) { // Max 5 seconds wait
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    // Return cache if available after waiting
    if (walletAdapterCache) {
      return walletAdapterCache;
    }
  }

  try {
    isInitializing = true;
    console.log('🔄 Loading wallet adapters with enhanced HMR-safe strategy...');

    // Load each wallet adapter individually with explicit functions
    const walletPromises = [
      loadPhantomWallet(),
      loadSolflareWallet(),
      loadBackpackWallet(),
      loadGlowWallet(),
    ];

    // Wait for all wallet adapters to load (or fail gracefully)
    const walletResults = await Promise.allSettled(walletPromises);

    // Analyze results for HMR-related and chunk loading failures
    const hmrFailures = walletResults.filter(result =>
      result.status === 'rejected' &&
      result.reason instanceof Error && (
        result.reason.message.includes('module factory') ||
        result.reason.message.includes('HMR') ||
        result.reason.message.includes('chunk') ||
        result.reason.message.includes('Loading chunk') ||
        result.reason.message.includes('Failed to load chunk') ||
        result.reason.message.includes('ChunkLoadError') ||
        result.reason.name === 'ChunkLoadError'
      )
    );

    // Separate chunk loading errors from other HMR errors
    const chunkErrors = hmrFailures.filter(failure =>
      failure.status === 'rejected' &&
      failure.reason instanceof Error && (
        failure.reason.message.includes('Loading chunk') ||
        failure.reason.message.includes('Failed to load chunk') ||
        failure.reason.message.includes('ChunkLoadError') ||
        failure.reason.name === 'ChunkLoadError'
      )
    );

    // If we have failures, log them specifically
    if (hmrFailures.length > 0) {
      console.warn(`⚠️ Detected ${hmrFailures.length} wallet adapter loading failures`);

      if (chunkErrors.length > 0) {
        console.warn(`   - ${chunkErrors.length} chunk loading errors detected`);
        chunkErrors.forEach((failure, index) => {
          if (failure.status === 'rejected') {
            console.warn(`     • Chunk Error ${index + 1}: ${failure.reason.message}`);
          }
        });
      }

      const otherErrors = hmrFailures.filter(f => !chunkErrors.includes(f));
      if (otherErrors.length > 0) {
        console.warn(`   - ${otherErrors.length} HMR/module factory errors detected`);
        otherErrors.forEach((failure, index) => {
          if (failure.status === 'rejected') {
            console.warn(`     • HMR Error ${index + 1}: ${failure.reason.message}`);
          }
        });
      }
    }

    // Filter out failed/null wallets and collect successful ones
    const wallets = walletResults
      .map(result => result.status === 'fulfilled' ? result.value : null)
      .filter(wallet => wallet !== null);

    // Update cache only if we have successful wallets or no HMR failures
    if (wallets.length > 0 || hmrFailures.length === 0) {
      walletAdapterCache = wallets;
      lastCacheTime = now;
    }

    // Enhanced graceful degradation with HMR awareness
    if (wallets.length === 0) {
      if (hmrFailures.length > 0) {
        console.warn('⚠️ All wallet adapters failed due to HMR module factory issues');
        console.log('🔄 Consider refreshing the page or waiting for HMR to stabilize');

        // Return cached wallets if available during HMR issues
        if (walletAdapterCache && walletAdapterCache.length > 0) {
          console.log('🔄 Using cached wallet adapters during HMR instability');
          return walletAdapterCache;
        }
      } else {
        console.warn('⚠️ No wallet adapters loaded successfully - implementing graceful degradation');
        console.log('🔧 Consider checking wallet adapter installations and network connectivity');
      }
    } else if (wallets.length < 4) {
      const successRate = ((wallets.length / 4) * 100).toFixed(0);
      console.warn(`⚠️ Only ${wallets.length}/4 wallet adapters loaded (${successRate}% success rate) - partial functionality available`);
    }

    console.log(`✅ Successfully loaded ${wallets.length}/4 wallet adapters`);
    return wallets;
  } catch (error) {
    console.error('❌ Critical error in wallet adapter loading:', error);

    // Enhanced error handling for HMR-related and chunk loading issues
    if (error instanceof Error) {
      const isChunkError = error.message.includes('Loading chunk') ||
                         error.message.includes('Failed to load chunk') ||
                         error.message.includes('ChunkLoadError') ||
                         error.name === 'ChunkLoadError';

      const isModuleError = error.message.includes('module factory') ||
                          error.message.includes('HMR') ||
                          error.message.includes('chunk');

      if (isChunkError) {
        console.warn('🔄 Critical chunk loading error detected in wallet loading:', error.message);
        console.log('💡 This may be due to network issues or Turbopack chunk generation problems');

        // Return cached wallets as fallback if available
        if (walletAdapterCache && walletAdapterCache.length > 0) {
          console.log('🔄 Falling back to cached wallet adapters due to chunk loading error');
          return walletAdapterCache;
        }
      } else if (isModuleError) {
        console.warn('🔄 Critical HMR/module factory error detected in wallet loading:', error.message);

        // Return cached wallets as fallback if available
        if (walletAdapterCache && walletAdapterCache.length > 0) {
          console.log('🔄 Falling back to cached wallet adapters due to HMR error');
          return walletAdapterCache;
        }
      }
    }

    // Return cached wallets as fallback if available
    if (walletAdapterCache) {
      console.log('🔄 Falling back to cached wallet adapters');
      return walletAdapterCache;
    }

    return [];
  } finally {
    isInitializing = false;
  }
};

// Wallet connection configuration
export const walletConfig = {
  autoConnect: false, // Disable autoConnect to prevent WalletNotReadyError
  localStorageKey: 'walletAdapter',
  onError: (error: Error) => {
    console.error('Wallet error:', error);
    // Handle specific wallet errors
    if (error.name === 'WalletNotReadyError') {
      console.warn('Wallet not ready. Please ensure your wallet extension is installed and unlocked.');
    } else if (error.name === 'WalletConnectionError') {
      console.warn('Failed to connect to wallet. Please try again.');
    }
  },
};

// Supported wallet names for UI display
export const SUPPORTED_WALLETS = [
  {
    name: 'Phantom',
    icon: '/icons/phantom.svg',
    url: 'https://phantom.app/',
    adapter: 'phantom',
  },
  {
    name: 'Solflare',
    icon: '/icons/solflare.svg',
    url: 'https://solflare.com/',
    adapter: 'solflare',
  },
  {
    name: 'Backpack',
    icon: '/icons/backpack.svg',
    url: 'https://backpack.app/',
    adapter: 'backpack',
  },
  {
    name: 'Glow',
    icon: '/icons/glow.svg',
    url: 'https://glow.app/',
    adapter: 'glow',
  },
] as const;

// Wallet readiness check utility
export const checkWalletReadiness = () => {
  // Return all false during SSR
  if (typeof window === 'undefined') {
    return {
      phantom: false,
      solflare: false,
      backpack: false,
      glow: false,
    };
  }

  try {
    const checks = {
      phantom: !!(window.solana?.isPhantom),
      solflare: !!(window.solflare?.isSolflare),
      backpack: !!(window.backpack?.isBackpack),
      glow: !!(window.glow),
    };

    return checks;
  } catch (error) {
    console.error('Error checking wallet readiness:', error);
    return {
      phantom: false,
      solflare: false,
      backpack: false,
      glow: false,
    };
  }
};

// Get available wallets
export const getAvailableWallets = () => {
  // Return empty array during SSR or when window is not available
  if (typeof window === 'undefined') {
    return [];
  }

  try {
    const readiness = checkWalletReadiness();

    return SUPPORTED_WALLETS.filter(wallet => {
      switch (wallet.adapter) {
        case 'phantom':
          return readiness.phantom;
        case 'solflare':
          return readiness.solflare;
        case 'backpack':
          return readiness.backpack;
        case 'glow':
          return readiness.glow;
        default:
          return false;
      }
    });
  } catch (error) {
    console.error('Error getting available wallets:', error);
    return [];
  }
};
